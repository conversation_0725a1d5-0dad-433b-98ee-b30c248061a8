from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from config import config
from .models import Base
import logging

logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        self.engine = create_async_engine(
            config.database_url,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300,
        )
        self.async_session = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )

    async def create_tables(self):
        """Создание таблиц в базе данных"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            raise

    def get_session(self):
        """Получение сессии базы данных"""
        return self.async_session()

    async def close(self):
        """Закрытие подключения к базе данных"""
        await self.engine.dispose()

# Глобальный экземпляр базы данных
database = Database()

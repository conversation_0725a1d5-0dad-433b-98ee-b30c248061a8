from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import re

Base = declarative_base()

class Product(Base):
    __tablename__ = 'products'
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    price = Column(Float, nullable=False)
    condition = Column(String, nullable=False, index=True)  # 'new' or 'used'
    model = Column(String, nullable=True, index=True)
    post_link = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.model and self.name:
            self.model = self.extract_model_from_name()
    
    def extract_model_from_name(self):
        """Извлекает модель из названия товара"""
        name_lower = self.name.lower()
        
        # Паттерны для поиска моделей iPhone
        patterns = [
            r'iphone\s*(\d+(?:\s*pro(?:\s*max)?)?)',
            r'(\d+(?:\s*pro(?:\s*max)?)?)',
            r'(xr|xs|x)',
            r'(se)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, name_lower)
            if match:
                model = match.group(1).strip()
                # Нормализация названий моделей
                if 'pro max' in model:
                    return model.replace(' ', ' Pro Max').title()
                elif 'pro' in model:
                    return model.replace(' ', ' Pro').title()
                elif model in ['xr', 'xs', 'x', 'se']:
                    return model.upper()
                else:
                    return model
        
        return 'Unknown'

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, index=True)
    telegram_id = Column(Integer, unique=True, nullable=False, index=True)
    username = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), onupdate=func.now())

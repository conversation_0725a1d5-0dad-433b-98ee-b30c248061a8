from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func, distinct
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from .models import Product, User
import logging

logger = logging.getLogger(__name__)

class ProductCRUD:
    @staticmethod
    async def create_product(session: AsyncSession, **product_data) -> Product:
        """Создание нового товара"""
        product = Product(**product_data)
        session.add(product)
        await session.commit()
        await session.refresh(product)
        return product
    
    @staticmethod
    async def get_products_by_condition(session: AsyncSession, condition: str) -> List[Product]:
        """Получение товаров по состоянию (new/used)"""
        result = await session.execute(
            select(Product).where(Product.condition == condition).order_by(Product.price.desc())
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_products_by_model_and_condition(session: AsyncSession, model: str, condition: str) -> List[Product]:
        """Получение товаров по модели и состоянию"""
        result = await session.execute(
            select(Product).where(
                Product.model == model,
                Product.condition == condition
            ).order_by(Product.price.desc())
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_unique_models_by_condition(session: AsyncSession, condition: str) -> List[str]:
        """Получение уникальных моделей по состоянию"""
        result = await session.execute(
            select(distinct(Product.model)).where(Product.condition == condition)
        )
        models = result.scalars().all()
        return [model for model in models if model and model != 'Unknown']
    
    @staticmethod
    async def get_price_range_by_model_and_condition(session: AsyncSession, model: str, condition: str) -> Dict[str, float]:
        """Получение диапазона цен для модели и состояния"""
        result = await session.execute(
            select(
                func.min(Product.price).label('min_price'),
                func.max(Product.price).label('max_price')
            ).where(
                Product.model == model,
                Product.condition == condition
            )
        )
        row = result.first()
        return {
            'min_price': row.min_price if row.min_price else 0,
            'max_price': row.max_price if row.max_price else 0
        }
    
    @staticmethod
    async def get_price_range_by_condition(session: AsyncSession, condition: str) -> Dict[str, float]:
        """Получение общего диапазона цен по состоянию"""
        result = await session.execute(
            select(
                func.min(Product.price).label('min_price'),
                func.max(Product.price).label('max_price')
            ).where(Product.condition == condition)
        )
        row = result.first()
        return {
            'min_price': row.min_price if row.min_price else 0,
            'max_price': row.max_price if row.max_price else 0
        }
    
    @staticmethod
    async def update_product(session: AsyncSession, product_id: int, **update_data) -> Optional[Product]:
        """Обновление товара"""
        result = await session.execute(select(Product).where(Product.id == product_id))
        product = result.scalar_one_or_none()
        if product:
            for key, value in update_data.items():
                setattr(product, key, value)
            await session.commit()
            await session.refresh(product)
        return product
    
    @staticmethod
    async def delete_all_products_by_condition(session: AsyncSession, condition: str):
        """Удаление всех товаров по состоянию"""
        await session.execute(delete(Product).where(Product.condition == condition))
        await session.commit()

class UserCRUD:
    @staticmethod
    async def create_or_update_user(session: AsyncSession, telegram_id: int, **user_data) -> User:
        """Создание или обновление пользователя"""
        result = await session.execute(select(User).where(User.telegram_id == telegram_id))
        user = result.scalar_one_or_none()
        
        if user:
            for key, value in user_data.items():
                setattr(user, key, value)
        else:
            user = User(telegram_id=telegram_id, **user_data)
            session.add(user)
        
        await session.commit()
        await session.refresh(user)
        return user
    
    @staticmethod
    async def get_user_by_telegram_id(session: AsyncSession, telegram_id: int) -> Optional[User]:
        """Получение пользователя по Telegram ID"""
        result = await session.execute(select(User).where(User.telegram_id == telegram_id))
        return result.scalar_one_or_none()
    
    @staticmethod
    async def is_admin(session: AsyncSession, telegram_id: int) -> bool:
        """Проверка, является ли пользователь администратором"""
        user = await UserCRUD.get_user_by_telegram_id(session, telegram_id)
        return user.is_admin if user else False

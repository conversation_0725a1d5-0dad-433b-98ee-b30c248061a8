2025-06-23 19:57:02,773 - __main__ - INFO - Initializing database...
2025-06-23 19:57:02,774 - database.database - ERROR - Error creating database tables: the greenlet library is required to use this function. No module named 'greenlet'
2025-06-23 19:57:02,774 - __main__ - ERROR - Error starting bot: the greenlet library is required to use this function. No module named 'greenlet'
2025-06-23 19:57:02,774 - __main__ - ERROR - Unexpected error: the greenlet library is required to use this function. No module named 'greenlet'
2025-06-23 19:57:23,031 - __main__ - INFO - Initializing database...
2025-06-23 19:57:23,149 - database.database - INFO - Database tables created successfully
2025-06-23 19:57:23,149 - __main__ - INFO - Initializing product updater...
2025-06-23 19:57:23,149 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 19:57:23,149 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 19:57:23,316 - parser.file_parser - INFO - Successfully updated 58 products from data/new_products.txt
2025-06-23 19:57:23,317 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 19:57:23,510 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 19:57:23,510 - parser.file_parser - INFO - Initial data load completed
2025-06-23 19:57:23,510 - __main__ - INFO - Starting file watcher...
2025-06-23 19:57:23,513 - parser.file_parser - INFO - File watcher started
2025-06-23 19:57:23,513 - __main__ - INFO - Starting bot...
2025-06-23 19:57:23,513 - aiogram.dispatcher - INFO - Start polling
2025-06-23 19:57:27,043 - aiogram.dispatcher - INFO - Run polling for bot @Ostatki_ap43_bot id=7981191240 - 'Остатки Appleshop'
2025-06-23 19:58:00,320 - aiogram.event - INFO - Update id=417910428 is not handled. Duration 40 ms by bot id=7981191240
2025-06-23 19:58:00,322 - aiogram.event - ERROR - Cause exception while process update id=417910428 by bot id=7981191240
DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $1: 5513789802 (value out of int32 range)
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::INTEGER]
[parameters: (5513789802,)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
Traceback (most recent call last):
  File "asyncpg/protocol/prepared_stmt.pyx", line 175, in asyncpg.protocol.protocol.PreparedStatementState._encode_bind_msg
  File "asyncpg/protocol/codecs/base.pyx", line 227, in asyncpg.protocol.protocol.Codec.encode
  File "asyncpg/protocol/codecs/base.pyx", line 129, in asyncpg.protocol.protocol.Codec.encode_scalar
  File "asyncpg/pgproto/./codecs/int.pyx", line 60, in asyncpg.pgproto.pgproto.int4_encode
OverflowError: value out of int32 range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 545, in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 176, in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 267, in __bind_execute
    data, status, _ = await self.__do_execute(
                      ^^^^^^^^^^^^^^^^^^^^^^^^
        lambda protocol: protocol.bind_execute(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
            self._state, args, '', limit, True, timeout))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 256, in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncpg/protocol/protocol.pyx", line 185, in bind_execute
  File "asyncpg/protocol/prepared_stmt.pyx", line 204, in asyncpg.protocol.protocol.PreparedStatementState._encode_bind_msg
asyncpg.exceptions.DataError: invalid input for query argument $1: 5513789802 (value out of int32 range)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.Error: <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $1: 5513789802 (value out of int32 range)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 20, in start_command
    await UserCRUD.create_or_update_user(
    ...<5 lines>...
    )
  File "/Users/<USER>/appleshopstats/stats/database/crud.py", line 103, in create_or_update_user
    result = await session.execute(select(User).where(User.telegram_id == telegram_id))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py", line 463, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $1: 5513789802 (value out of int32 range)
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::INTEGER]
[parameters: (5513789802,)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-06-23 19:58:06,541 - aiogram.event - INFO - Update id=417910429 is not handled. Duration 20 ms by bot id=7981191240
2025-06-23 19:58:06,542 - aiogram.event - ERROR - Cause exception while process update id=417910429 by bot id=7981191240
DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $1: 5513789802 (value out of int32 range)
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::INTEGER]
[parameters: (5513789802,)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
Traceback (most recent call last):
  File "asyncpg/protocol/prepared_stmt.pyx", line 175, in asyncpg.protocol.protocol.PreparedStatementState._encode_bind_msg
  File "asyncpg/protocol/codecs/base.pyx", line 227, in asyncpg.protocol.protocol.Codec.encode
  File "asyncpg/protocol/codecs/base.pyx", line 129, in asyncpg.protocol.protocol.Codec.encode_scalar
  File "asyncpg/pgproto/./codecs/int.pyx", line 60, in asyncpg.pgproto.pgproto.int4_encode
OverflowError: value out of int32 range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 545, in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 176, in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 267, in __bind_execute
    data, status, _ = await self.__do_execute(
                      ^^^^^^^^^^^^^^^^^^^^^^^^
        lambda protocol: protocol.bind_execute(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
            self._state, args, '', limit, True, timeout))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 256, in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncpg/protocol/protocol.pyx", line 185, in bind_execute
  File "asyncpg/protocol/prepared_stmt.pyx", line 204, in asyncpg.protocol.protocol.PreparedStatementState._encode_bind_msg
asyncpg.exceptions.DataError: invalid input for query argument $1: 5513789802 (value out of int32 range)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.Error: <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $1: 5513789802 (value out of int32 range)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 20, in start_command
    await UserCRUD.create_or_update_user(
    ...<5 lines>...
    )
  File "/Users/<USER>/appleshopstats/stats/database/crud.py", line 103, in create_or_update_user
    result = await session.execute(select(User).where(User.telegram_id == telegram_id))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py", line 463, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.DataError'>: invalid input for query argument $1: 5513789802 (value out of int32 range)
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::INTEGER]
[parameters: (5513789802,)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-06-23 19:58:57,539 - __main__ - INFO - Initializing database...
2025-06-23 19:58:57,604 - database.database - INFO - Database tables created successfully
2025-06-23 19:58:57,604 - __main__ - INFO - Initializing product updater...
2025-06-23 19:58:57,604 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 19:58:57,604 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 19:58:57,743 - parser.file_parser - INFO - Successfully updated 58 products from data/new_products.txt
2025-06-23 19:58:57,743 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 19:58:57,976 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 19:58:57,976 - parser.file_parser - INFO - Initial data load completed
2025-06-23 19:58:57,976 - __main__ - INFO - Starting file watcher...
2025-06-23 19:58:57,977 - parser.file_parser - INFO - File watcher started
2025-06-23 19:58:57,977 - __main__ - INFO - Starting bot...
2025-06-23 19:58:57,977 - aiogram.dispatcher - INFO - Start polling
2025-06-23 19:58:58,686 - aiogram.dispatcher - INFO - Run polling for bot @Ostatki_ap43_bot id=7981191240 - 'Остатки Appleshop'
2025-06-23 19:59:31,494 - aiogram.event - INFO - Update id=417910430 is not handled. Duration 41 ms by bot id=7981191240
2025-06-23 19:59:31,494 - aiogram.event - ERROR - Cause exception while process update id=417910430 by bot id=7981191240
DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.NumericValueOutOfRangeError'>: integer out of range
[SQL: INSERT INTO users (telegram_id, username, first_name, last_name, is_admin, last_activity) VALUES ($1::BIGINT, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::BOOLEAN, $6::TIMESTAMP WITH TIME ZONE) RETURNING users.id, users.created_at]
[parameters: (5513789802, 'gabagoolioner', 'Gabagoolioner', None, False, None)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 545, in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 176, in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 267, in __bind_execute
    data, status, _ = await self.__do_execute(
                      ^^^^^^^^^^^^^^^^^^^^^^^^
        lambda protocol: protocol.bind_execute(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
            self._state, args, '', limit, True, timeout))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 256, in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncpg/protocol/protocol.pyx", line 206, in bind_execute
asyncpg.exceptions.NumericValueOutOfRangeError: integer out of range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.Error: <class 'asyncpg.exceptions.NumericValueOutOfRangeError'>: integer out of range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 20, in start_command
    await UserCRUD.create_or_update_user(
    ...<5 lines>...
    )
  File "/Users/<USER>/appleshopstats/stats/database/crud.py", line 113, in create_or_update_user
    await session.commit()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py", line 1014, in commit
    await greenlet_spawn(self.sync_session.commit)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 203, in greenlet_spawn
    result = context.switch(value)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2032, in commit
    trans.commit(_to_root=True)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "<string>", line 2, in commit
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 1313, in commit
    self._prepare_impl()
    ~~~~~~~~~~~~~~~~~~^^
  File "<string>", line 2, in _prepare_impl
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 1288, in _prepare_impl
    self.session.flush()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4345, in flush
    self._flush(objects)
    ~~~~~~~~~~~^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4480, in _flush
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4441, in _flush
    flush_context.execute()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py", line 466, in execute
    rec.execute(self)
    ~~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self.mapper,
        ^^^^^^^^^^^^
        uow.states_for_mapper_hierarchy(self.mapper, False, False),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        uow,
        ^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py", line 93, in save_obj
    _emit_insert_statements(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        base_mapper,
        ^^^^^^^^^^^^
    ...<3 lines>...
        insert,
        ^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
        statement,
        params,
        execution_options=execution_options,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.NumericValueOutOfRangeError'>: integer out of range
[SQL: INSERT INTO users (telegram_id, username, first_name, last_name, is_admin, last_activity) VALUES ($1::BIGINT, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::BOOLEAN, $6::TIMESTAMP WITH TIME ZONE) RETURNING users.id, users.created_at]
[parameters: (5513789802, 'gabagoolioner', 'Gabagoolioner', None, False, None)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-06-23 19:59:35,343 - aiogram.event - INFO - Update id=417910431 is not handled. Duration 14 ms by bot id=7981191240
2025-06-23 19:59:35,343 - aiogram.event - ERROR - Cause exception while process update id=417910431 by bot id=7981191240
DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.NumericValueOutOfRangeError'>: integer out of range
[SQL: INSERT INTO users (telegram_id, username, first_name, last_name, is_admin, last_activity) VALUES ($1::BIGINT, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::BOOLEAN, $6::TIMESTAMP WITH TIME ZONE) RETURNING users.id, users.created_at]
[parameters: (5513789802, 'gabagoolioner', 'Gabagoolioner', None, False, None)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 545, in _prepare_and_execute
    self._rows = deque(await prepared_stmt.fetch(*parameters))
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 176, in fetch
    data = await self.__bind_execute(args, 0, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 267, in __bind_execute
    data, status, _ = await self.__do_execute(
                      ^^^^^^^^^^^^^^^^^^^^^^^^
        lambda protocol: protocol.bind_execute(
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
            self._state, args, '', limit, True, timeout))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/prepared_stmt.py", line 256, in __do_execute
    return await executor(protocol)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncpg/protocol/protocol.pyx", line 206, in bind_execute
asyncpg.exceptions.NumericValueOutOfRangeError: integer out of range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.Error: <class 'asyncpg.exceptions.NumericValueOutOfRangeError'>: integer out of range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 20, in start_command
    await UserCRUD.create_or_update_user(
    ...<5 lines>...
    )
  File "/Users/<USER>/appleshopstats/stats/database/crud.py", line 113, in create_or_update_user
    await session.commit()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py", line 1014, in commit
    await greenlet_spawn(self.sync_session.commit)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 203, in greenlet_spawn
    result = context.switch(value)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2032, in commit
    trans.commit(_to_root=True)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "<string>", line 2, in commit
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 1313, in commit
    self._prepare_impl()
    ~~~~~~~~~~~~~~~~~~^^
  File "<string>", line 2, in _prepare_impl
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 1288, in _prepare_impl
    self.session.flush()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4345, in flush
    self._flush(objects)
    ~~~~~~~~~~~^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4480, in _flush
    with util.safe_reraise():
         ~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 4441, in _flush
    flush_context.execute()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py", line 466, in execute
    rec.execute(self)
    ~~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self.mapper,
        ^^^^^^^^^^^^
        uow.states_for_mapper_hierarchy(self.mapper, False, False),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        uow,
        ^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py", line 93, in save_obj
    _emit_insert_statements(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        base_mapper,
        ^^^^^^^^^^^^
    ...<3 lines>...
        insert,
        ^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
        statement,
        params,
        execution_options=execution_options,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.DBAPIError: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.NumericValueOutOfRangeError'>: integer out of range
[SQL: INSERT INTO users (telegram_id, username, first_name, last_name, is_admin, last_activity) VALUES ($1::BIGINT, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::BOOLEAN, $6::TIMESTAMP WITH TIME ZONE) RETURNING users.id, users.created_at]
[parameters: (5513789802, 'gabagoolioner', 'Gabagoolioner', None, False, None)]
(Background on this error at: https://sqlalche.me/e/20/dbapi)
2025-06-23 20:01:00,242 - __main__ - INFO - Initializing database...
2025-06-23 20:01:00,309 - database.database - INFO - Database tables created successfully
2025-06-23 20:01:00,309 - __main__ - INFO - Initializing product updater...
2025-06-23 20:01:00,309 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 20:01:00,309 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 20:01:00,493 - parser.file_parser - INFO - Successfully updated 58 products from data/new_products.txt
2025-06-23 20:01:00,494 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 20:01:00,742 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 20:01:00,742 - parser.file_parser - INFO - Initial data load completed
2025-06-23 20:01:00,742 - __main__ - INFO - Starting file watcher...
2025-06-23 20:01:00,743 - parser.file_parser - INFO - File watcher started
2025-06-23 20:01:00,743 - __main__ - INFO - Starting bot...
2025-06-23 20:01:00,743 - aiogram.dispatcher - INFO - Start polling
2025-06-23 20:01:01,309 - aiogram.dispatcher - INFO - Run polling for bot @Ostatki_ap43_bot id=7981191240 - 'Остатки Appleshop'
2025-06-23 20:01:02,527 - aiogram.event - INFO - Update id=417910432 is not handled. Duration 10 ms by bot id=7981191240
2025-06-23 20:01:02,527 - aiogram.event - ERROR - Cause exception while process update id=417910432 by bot id=7981191240
ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::VARCHAR]
[parameters: ('5513789802',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 521, in _prepare_and_execute
    prepared_stmt, attributes = await adapt_connection._prepare(
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        operation, self._invalidate_schema_cache_asof
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 768, in _prepare
    prepared_stmt = await self._connection.prepare(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        operation, name=self._prepared_statement_name_func()
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 635, in prepare
    return await self._prepare(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 653, in _prepare
    stmt = await self._get_statement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 432, in _get_statement
    statement = await self._protocol.prepare(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "asyncpg/protocol/protocol.pyx", line 165, in prepare
asyncpg.exceptions.UndefinedFunctionError: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.ProgrammingError: <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 20, in start_command
    await UserCRUD.create_or_update_user(
    ...<5 lines>...
    )
  File "/Users/<USER>/appleshopstats/stats/database/crud.py", line 104, in create_or_update_user
    result = await session.execute(select(User).where(User.telegram_id == telegram_id_str))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py", line 463, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::VARCHAR]
[parameters: ('5513789802',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-23 20:01:06,040 - aiogram.event - INFO - Update id=417910433 is not handled. Duration 19 ms by bot id=7981191240
2025-06-23 20:01:06,041 - aiogram.event - ERROR - Cause exception while process update id=417910433 by bot id=7981191240
ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::VARCHAR]
[parameters: ('5513789802',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 521, in _prepare_and_execute
    prepared_stmt, attributes = await adapt_connection._prepare(
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        operation, self._invalidate_schema_cache_asof
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 768, in _prepare
    prepared_stmt = await self._connection.prepare(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        operation, name=self._prepared_statement_name_func()
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 635, in prepare
    return await self._prepare(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 653, in _prepare
    stmt = await self._get_statement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 432, in _get_statement
    statement = await self._protocol.prepare(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "asyncpg/protocol/protocol.pyx", line 165, in prepare
asyncpg.exceptions.UndefinedFunctionError: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.ProgrammingError: <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 20, in start_command
    await UserCRUD.create_or_update_user(
    ...<5 lines>...
    )
  File "/Users/<USER>/appleshopstats/stats/database/crud.py", line 104, in create_or_update_user
    result = await session.execute(select(User).where(User.telegram_id == telegram_id_str))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py", line 463, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::VARCHAR]
[parameters: ('5513789802',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-23 20:01:12,365 - aiogram.event - INFO - Update id=417910434 is not handled. Duration 8 ms by bot id=7981191240
2025-06-23 20:01:12,366 - aiogram.event - ERROR - Cause exception while process update id=417910434 by bot id=7981191240
ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::VARCHAR]
[parameters: ('5513789802',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 521, in _prepare_and_execute
    prepared_stmt, attributes = await adapt_connection._prepare(
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        operation, self._invalidate_schema_cache_asof
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 768, in _prepare
    prepared_stmt = await self._connection.prepare(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        operation, name=self._prepared_statement_name_func()
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 635, in prepare
    return await self._prepare(
           ^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 653, in _prepare
    stmt = await self._get_statement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/asyncpg/connection.py", line 432, in _get_statement
    statement = await self._protocol.prepare(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "asyncpg/protocol/protocol.pyx", line 165, in prepare
asyncpg.exceptions.UndefinedFunctionError: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.ProgrammingError: <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 20, in start_command
    await UserCRUD.create_or_update_user(
    ...<5 lines>...
    )
  File "/Users/<USER>/appleshopstats/stats/database/crud.py", line 104, in create_or_update_user
    result = await session.execute(select(User).where(User.telegram_id == telegram_id_str))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/session.py", line 463, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 580, in execute
    self._adapt_connection.await_(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._prepare_and_execute(operation, parameters)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 558, in _prepare_and_execute
    self._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 508, in _handle_exception
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 792, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedFunctionError'>: operator does not exist: integer = character varying
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
[SQL: SELECT users.id, users.telegram_id, users.username, users.first_name, users.last_name, users.is_admin, users.created_at, users.last_activity 
FROM users 
WHERE users.telegram_id = $1::VARCHAR]
[parameters: ('5513789802',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-23 20:01:52,267 - __main__ - INFO - Initializing database...
2025-06-23 20:01:52,312 - database.database - INFO - Database tables created successfully
2025-06-23 20:01:52,312 - __main__ - INFO - Initializing product updater...
2025-06-23 20:01:52,312 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 20:01:52,312 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 20:01:52,436 - parser.file_parser - INFO - Successfully updated 58 products from data/new_products.txt
2025-06-23 20:01:52,436 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 20:01:52,738 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 20:01:52,738 - parser.file_parser - INFO - Initial data load completed
2025-06-23 20:01:52,738 - __main__ - INFO - Starting file watcher...
2025-06-23 20:01:52,739 - parser.file_parser - INFO - File watcher started
2025-06-23 20:01:52,739 - __main__ - INFO - Starting bot...
2025-06-23 20:01:52,740 - aiogram.dispatcher - INFO - Start polling
2025-06-23 20:01:53,232 - aiogram.dispatcher - INFO - Run polling for bot @Ostatki_ap43_bot id=7981191240 - 'Остатки Appleshop'
2025-06-23 20:02:16,471 - aiogram.event - INFO - Update id=417910435 is handled. Duration 485 ms by bot id=7981191240
2025-06-23 20:02:19,989 - aiogram.event - INFO - Update id=417910436 is handled. Duration 485 ms by bot id=7981191240
2025-06-23 20:02:25,019 - aiogram.event - INFO - Update id=417910437 is handled. Duration 614 ms by bot id=7981191240
2025-06-23 20:02:30,164 - aiogram.event - INFO - Update id=417910438 is handled. Duration 557 ms by bot id=7981191240
2025-06-23 20:02:31,727 - aiogram.event - INFO - Update id=417910439 is handled. Duration 684 ms by bot id=7981191240
2025-06-23 20:02:36,557 - aiogram.event - INFO - Update id=417910440 is handled. Duration 554 ms by bot id=7981191240
2025-06-23 20:02:39,803 - aiogram.event - INFO - Update id=417910441 is handled. Duration 357 ms by bot id=7981191240
2025-06-23 20:02:41,073 - aiogram.event - INFO - Update id=417910442 is handled. Duration 360 ms by bot id=7981191240
2025-06-23 20:02:43,110 - aiogram.event - INFO - Update id=417910443 is handled. Duration 539 ms by bot id=7981191240
2025-06-23 20:02:52,136 - aiogram.event - INFO - Update id=417910444 is handled. Duration 342 ms by bot id=7981191240
2025-06-23 20:02:53,538 - aiogram.event - INFO - Update id=417910445 is handled. Duration 387 ms by bot id=7981191240
2025-06-23 20:03:01,410 - aiogram.event - INFO - Update id=417910446 is handled. Duration 570 ms by bot id=7981191240
2025-06-23 20:03:06,659 - aiogram.event - INFO - Update id=417910447 is handled. Duration 1084 ms by bot id=7981191240
2025-06-23 20:03:10,364 - aiogram.event - INFO - Update id=417910448 is handled. Duration 516 ms by bot id=7981191240
2025-06-23 20:03:21,827 - aiogram.event - INFO - Update id=417910449 is handled. Duration 475 ms by bot id=7981191240
2025-06-23 20:03:25,324 - aiogram.event - INFO - Update id=417910450 is handled. Duration 455 ms by bot id=7981191240
2025-06-23 20:03:27,046 - aiogram.event - INFO - Update id=417910451 is handled. Duration 402 ms by bot id=7981191240
2025-06-23 20:03:28,465 - aiogram.event - INFO - Update id=417910452 is handled. Duration 399 ms by bot id=7981191240
2025-06-23 20:03:29,797 - aiogram.event - INFO - Update id=417910453 is handled. Duration 524 ms by bot id=7981191240
2025-06-23 20:03:41,291 - aiogram.event - INFO - Update id=417910454 is handled. Duration 372 ms by bot id=7981191240
2025-06-23 20:03:43,864 - aiogram.event - INFO - Update id=417910455 is handled. Duration 727 ms by bot id=7981191240
2025-06-23 20:03:45,369 - aiogram.event - INFO - Update id=417910456 is handled. Duration 508 ms by bot id=7981191240
2025-06-23 20:03:47,777 - aiogram.event - INFO - Update id=417910457 is handled. Duration 859 ms by bot id=7981191240
2025-06-23 20:03:51,034 - aiogram.event - INFO - Update id=417910458 is handled. Duration 584 ms by bot id=7981191240
2025-06-23 20:03:54,025 - aiogram.event - INFO - Update id=417910459 is handled. Duration 487 ms by bot id=7981191240
2025-06-23 20:03:56,469 - aiogram.event - INFO - Update id=417910460 is handled. Duration 564 ms by bot id=7981191240
2025-06-23 20:03:57,802 - aiogram.event - INFO - Update id=417910461 is handled. Duration 522 ms by bot id=7981191240
2025-06-23 20:04:01,862 - aiogram.event - INFO - Update id=417910462 is handled. Duration 336 ms by bot id=7981191240
2025-06-23 20:04:03,534 - aiogram.event - INFO - Update id=417910463 is handled. Duration 430 ms by bot id=7981191240
2025-06-23 20:04:05,315 - aiogram.event - INFO - Update id=417910464 is handled. Duration 329 ms by bot id=7981191240
2025-06-23 20:04:08,056 - aiogram.event - INFO - Update id=417910465 is handled. Duration 452 ms by bot id=7981191240
2025-06-23 20:04:10,306 - aiogram.event - INFO - Update id=417910466 is handled. Duration 397 ms by bot id=7981191240
2025-06-23 20:04:13,130 - aiogram.event - INFO - Update id=417910467 is handled. Duration 503 ms by bot id=7981191240
2025-06-23 20:04:14,457 - aiogram.event - INFO - Update id=417910468 is handled. Duration 340 ms by bot id=7981191240
2025-06-23 20:04:17,107 - aiogram.event - INFO - Update id=417910469 is handled. Duration 407 ms by bot id=7981191240
2025-06-23 20:04:18,854 - aiogram.event - INFO - Update id=417910470 is handled. Duration 783 ms by bot id=7981191240
2025-06-23 20:04:20,615 - aiogram.event - INFO - Update id=417910471 is handled. Duration 620 ms by bot id=7981191240
2025-06-23 20:04:34,662 - aiogram.event - INFO - Update id=417910472 is handled. Duration 379 ms by bot id=7981191240
2025-06-23 20:04:37,648 - aiogram.event - INFO - Update id=417910473 is handled. Duration 975 ms by bot id=7981191240
2025-06-23 20:04:38,820 - aiogram.event - INFO - Update id=417910474 is handled. Duration 425 ms by bot id=7981191240
2025-06-23 20:04:42,416 - aiogram.event - INFO - Update id=417910475 is handled. Duration 543 ms by bot id=7981191240
2025-06-23 20:04:45,028 - aiogram.event - INFO - Update id=417910476 is handled. Duration 337 ms by bot id=7981191240
2025-06-23 20:04:49,507 - aiogram.event - INFO - Update id=417910477 is handled. Duration 2115 ms by bot id=7981191240
2025-06-23 20:04:51,351 - aiogram.event - INFO - Update id=417910478 is handled. Duration 442 ms by bot id=7981191240
2025-06-23 20:04:53,667 - aiogram.event - INFO - Update id=417910479 is handled. Duration 444 ms by bot id=7981191240
2025-06-23 20:04:57,440 - aiogram.event - INFO - Update id=417910480 is handled. Duration 352 ms by bot id=7981191240
2025-06-23 20:05:01,146 - aiogram.event - INFO - Update id=417910481 is handled. Duration 495 ms by bot id=7981191240
2025-06-23 20:05:02,997 - aiogram.event - INFO - Update id=417910482 is handled. Duration 800 ms by bot id=7981191240
2025-06-23 20:05:06,125 - aiogram.event - INFO - Update id=417910483 is handled. Duration 770 ms by bot id=7981191240
2025-06-23 20:05:08,044 - aiogram.event - INFO - Update id=417910484 is handled. Duration 525 ms by bot id=7981191240
2025-06-23 20:05:09,783 - aiogram.event - INFO - Update id=417910485 is handled. Duration 779 ms by bot id=7981191240
2025-06-23 20:05:12,168 - aiogram.event - INFO - Update id=417910486 is handled. Duration 369 ms by bot id=7981191240
2025-06-23 20:05:15,208 - aiogram.event - INFO - Update id=417910487 is handled. Duration 630 ms by bot id=7981191240
2025-06-23 20:05:18,748 - aiogram.event - INFO - Update id=417910488 is handled. Duration 359 ms by bot id=7981191240
2025-06-23 20:05:20,383 - aiogram.event - INFO - Update id=417910489 is handled. Duration 329 ms by bot id=7981191240
2025-06-23 20:05:21,479 - aiogram.event - INFO - Update id=417910490 is handled. Duration 327 ms by bot id=7981191240
2025-06-23 20:05:24,001 - aiogram.event - INFO - Update id=417910491 is handled. Duration 385 ms by bot id=7981191240
2025-06-23 20:05:51,642 - aiogram.event - INFO - Update id=417910492 is handled. Duration 550 ms by bot id=7981191240
2025-06-23 20:05:53,423 - aiogram.event - INFO - Update id=417910493 is handled. Duration 492 ms by bot id=7981191240
2025-06-23 20:05:55,689 - aiogram.event - INFO - Update id=417910494 is handled. Duration 183 ms by bot id=7981191240
2025-06-23 20:06:01,931 - aiogram.event - INFO - Update id=417910495 is handled. Duration 682 ms by bot id=7981191240
2025-06-23 20:06:04,626 - aiogram.event - INFO - Update id=417910496 is handled. Duration 582 ms by bot id=7981191240
2025-06-23 20:06:07,849 - aiogram.event - INFO - Update id=417910497 is handled. Duration 700 ms by bot id=7981191240
2025-06-23 20:06:09,113 - aiogram.event - INFO - Update id=417910498 is handled. Duration 364 ms by bot id=7981191240
2025-06-23 20:06:11,627 - aiogram.event - INFO - Update id=417910499 is handled. Duration 389 ms by bot id=7981191240
2025-06-23 20:06:35,838 - aiogram.event - INFO - Update id=417910500 is handled. Duration 348 ms by bot id=7981191240
2025-06-23 20:06:37,823 - aiogram.event - INFO - Update id=417910501 is handled. Duration 941 ms by bot id=7981191240
2025-06-23 20:06:42,146 - aiogram.event - INFO - Update id=417910502 is handled. Duration 319 ms by bot id=7981191240
2025-06-23 20:06:56,400 - aiogram.event - INFO - Update id=417910503 is handled. Duration 408 ms by bot id=7981191240
2025-06-23 20:06:59,757 - aiogram.event - INFO - Update id=417910504 is handled. Duration 398 ms by bot id=7981191240
2025-06-23 20:07:03,695 - aiogram.event - INFO - Update id=417910505 is handled. Duration 346 ms by bot id=7981191240
2025-06-23 20:07:06,774 - aiogram.event - INFO - Update id=417910506 is handled. Duration 475 ms by bot id=7981191240
2025-06-23 20:07:23,595 - aiogram.event - INFO - Update id=417910507 is handled. Duration 532 ms by bot id=7981191240
2025-06-23 20:07:25,547 - aiogram.event - INFO - Update id=417910508 is handled. Duration 330 ms by bot id=7981191240
2025-06-23 20:07:33,119 - __main__ - INFO - Initializing database...
2025-06-23 20:07:33,172 - database.database - INFO - Database tables created successfully
2025-06-23 20:07:33,172 - __main__ - INFO - Initializing product updater...
2025-06-23 20:07:33,172 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 20:07:33,172 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 20:07:33,304 - parser.file_parser - INFO - Successfully updated 59 products from data/new_products.txt
2025-06-23 20:07:33,304 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 20:07:33,512 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 20:07:33,513 - parser.file_parser - INFO - Initial data load completed
2025-06-23 20:07:33,513 - __main__ - INFO - Starting file watcher...
2025-06-23 20:07:33,514 - parser.file_parser - INFO - File watcher started
2025-06-23 20:07:33,514 - __main__ - INFO - Starting bot...
2025-06-23 20:07:33,514 - aiogram.dispatcher - INFO - Start polling
2025-06-23 20:07:34,210 - aiogram.dispatcher - INFO - Run polling for bot @Ostatki_ap43_bot id=7981191240 - 'Остатки Appleshop'
2025-06-23 20:07:35,077 - aiogram.event - INFO - Update id=417910510 is not handled. Duration 720 ms by bot id=7981191240
2025-06-23 20:07:35,077 - aiogram.event - ERROR - Cause exception while process update id=417910510 by bot id=7981191240
TelegramBadRequest: Telegram server says - Bad Request: message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 152, in by_models
    await callback.message.edit_text(
    ...<2 lines>...
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/methods/base.py", line 84, in emit
    return await bot(self)
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/client/bot.py", line 478, in __call__
    return await self.session(self, method, timeout=request_timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/client/session/base.py", line 254, in __call__
    return cast(TelegramType, await middleware(bot, method))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/client/session/aiohttp.py", line 185, in make_request
    response = self.check_response(
        bot=bot, method=method, status_code=resp.status, content=raw_result
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/client/session/base.py", line 120, in check_response
    raise TelegramBadRequest(method=method, message=description)
aiogram.exceptions.TelegramBadRequest: Telegram server says - Bad Request: message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-06-23 20:07:35,374 - aiogram.event - INFO - Update id=417910511 is handled. Duration 1017 ms by bot id=7981191240
2025-06-23 20:07:37,280 - aiogram.event - INFO - Update id=417910512 is handled. Duration 329 ms by bot id=7981191240
2025-06-23 20:07:40,458 - aiogram.event - INFO - Update id=417910513 is handled. Duration 370 ms by bot id=7981191240
2025-06-23 20:07:43,386 - aiogram.event - INFO - Update id=417910514 is handled. Duration 434 ms by bot id=7981191240
2025-06-23 20:07:45,184 - aiogram.event - INFO - Update id=417910515 is handled. Duration 868 ms by bot id=7981191240
2025-06-23 20:07:46,620 - aiogram.event - INFO - Update id=417910516 is handled. Duration 397 ms by bot id=7981191240
2025-06-23 20:07:48,108 - aiogram.event - INFO - Update id=417910517 is handled. Duration 527 ms by bot id=7981191240
2025-06-23 20:07:56,820 - aiogram.event - INFO - Update id=417910518 is handled. Duration 471 ms by bot id=7981191240
2025-06-23 20:07:58,931 - bot.handlers.user - INFO - User 5513789802 (gabagoolioner) started the bot
2025-06-23 20:07:59,198 - aiogram.event - INFO - Update id=417910519 is handled. Duration 268 ms by bot id=7981191240
2025-06-23 20:08:01,856 - aiogram.event - INFO - Update id=417910520 is handled. Duration 449 ms by bot id=7981191240
2025-06-23 20:08:03,487 - aiogram.event - INFO - Update id=417910521 is handled. Duration 462 ms by bot id=7981191240
2025-06-23 20:08:05,586 - aiogram.event - INFO - Update id=417910522 is handled. Duration 452 ms by bot id=7981191240
2025-06-23 20:08:07,898 - aiogram.event - INFO - Update id=417910523 is handled. Duration 496 ms by bot id=7981191240
2025-06-23 20:08:09,443 - aiogram.event - INFO - Update id=417910524 is handled. Duration 393 ms by bot id=7981191240
2025-06-23 20:08:10,871 - aiogram.event - INFO - Update id=417910525 is handled. Duration 384 ms by bot id=7981191240
2025-06-23 20:08:26,599 - aiogram.event - INFO - Update id=417910526 is handled. Duration 392 ms by bot id=7981191240
2025-06-23 20:08:38,246 - __main__ - INFO - Initializing database...
2025-06-23 20:08:38,305 - database.database - INFO - Database tables created successfully
2025-06-23 20:08:38,305 - __main__ - INFO - Initializing product updater...
2025-06-23 20:08:38,305 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 20:08:38,305 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 20:08:38,446 - parser.file_parser - INFO - Successfully updated 59 products from data/new_products.txt
2025-06-23 20:08:38,447 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 20:08:38,684 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 20:08:38,684 - parser.file_parser - INFO - Initial data load completed
2025-06-23 20:08:38,684 - __main__ - INFO - Starting file watcher...
2025-06-23 20:08:38,685 - parser.file_parser - INFO - File watcher started
2025-06-23 20:08:38,685 - __main__ - INFO - Starting bot...
2025-06-23 20:08:38,685 - aiogram.dispatcher - INFO - Start polling
2025-06-23 20:08:39,337 - aiogram.dispatcher - INFO - Run polling for bot @Ostatki_ap43_bot id=7981191240 - 'Остатки Appleshop'
2025-06-23 20:08:39,506 - bot.handlers.user - INFO - User 5513789802 (gabagoolioner) started the bot
2025-06-23 20:08:40,089 - aiogram.event - INFO - Update id=417910527 is handled. Duration 583 ms by bot id=7981191240
2025-06-23 20:08:43,939 - aiogram.event - INFO - Update id=417910528 is handled. Duration 373 ms by bot id=7981191240
2025-06-23 20:08:45,920 - aiogram.event - INFO - Update id=417910529 is handled. Duration 358 ms by bot id=7981191240
2025-06-23 20:08:47,089 - aiogram.event - INFO - Update id=417910530 is handled. Duration 379 ms by bot id=7981191240
2025-06-23 20:08:50,045 - aiogram.event - INFO - Update id=417910531 is handled. Duration 611 ms by bot id=7981191240
2025-06-23 20:08:51,412 - aiogram.event - INFO - Update id=417910532 is handled. Duration 609 ms by bot id=7981191240
2025-06-23 20:08:53,375 - aiogram.event - INFO - Update id=417910533 is handled. Duration 482 ms by bot id=7981191240
2025-06-23 20:09:01,483 - aiogram.event - INFO - Update id=417910534 is handled. Duration 380 ms by bot id=7981191240
2025-06-23 20:09:03,391 - aiogram.event - INFO - Update id=417910535 is handled. Duration 420 ms by bot id=7981191240
2025-06-23 20:09:06,642 - aiogram.event - INFO - Update id=417910536 is handled. Duration 478 ms by bot id=7981191240
2025-06-23 20:09:10,275 - aiogram.event - INFO - Update id=417910537 is handled. Duration 459 ms by bot id=7981191240
2025-06-23 20:09:11,796 - aiogram.event - INFO - Update id=417910538 is handled. Duration 378 ms by bot id=7981191240
2025-06-23 20:09:20,815 - aiogram.event - INFO - Update id=417910539 is handled. Duration 267 ms by bot id=7981191240
2025-06-23 20:09:24,428 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 20:09:24,428 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 20:09:24,698 - parser.file_parser - INFO - Successfully updated 59 products from data/new_products.txt
2025-06-23 20:09:24,699 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 20:09:24,971 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 20:09:24,971 - parser.file_parser - INFO - Initial data load completed
2025-06-23 20:09:25,156 - aiogram.event - INFO - Update id=417910540 is handled. Duration 884 ms by bot id=7981191240
2025-06-23 20:09:29,571 - aiogram.event - INFO - Update id=417910541 is handled. Duration 330 ms by bot id=7981191240
2025-06-23 20:09:33,921 - aiogram.event - INFO - Update id=417910542 is handled. Duration 350 ms by bot id=7981191240
2025-06-23 20:09:35,762 - aiogram.event - INFO - Update id=417910543 is handled. Duration 410 ms by bot id=7981191240
2025-06-23 20:09:41,098 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 20:09:41,099 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 20:09:41,287 - parser.file_parser - INFO - Successfully updated 59 products from data/new_products.txt
2025-06-23 20:09:41,288 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 20:09:41,539 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 20:09:41,539 - parser.file_parser - INFO - Initial data load completed
2025-06-23 20:09:41,858 - aiogram.event - INFO - Update id=417910544 is handled. Duration 1204 ms by bot id=7981191240
2025-06-23 20:09:52,572 - aiogram.event - INFO - Update id=417910545 is handled. Duration 383 ms by bot id=7981191240
2025-06-23 20:09:56,790 - bot.handlers.user - INFO - User 5513789802 (gabagoolioner) started the bot
2025-06-23 20:09:56,985 - aiogram.event - INFO - Update id=417910546 is handled. Duration 195 ms by bot id=7981191240
2025-06-23 20:09:59,323 - aiogram.event - INFO - Update id=417910547 is handled. Duration 461 ms by bot id=7981191240
2025-06-23 20:10:01,207 - aiogram.event - INFO - Update id=417910548 is handled. Duration 490 ms by bot id=7981191240
2025-06-23 20:10:22,798 - aiogram.event - INFO - Update id=417910549 is handled. Duration 398 ms by bot id=7981191240
2025-06-23 20:10:25,049 - aiogram.event - INFO - Update id=417910550 is handled. Duration 609 ms by bot id=7981191240
2025-06-23 20:10:28,994 - aiogram.event - INFO - Update id=417910551 is handled. Duration 448 ms by bot id=7981191240
2025-06-23 20:10:33,035 - aiogram.event - INFO - Update id=417910552 is handled. Duration 368 ms by bot id=7981191240
2025-06-23 20:10:38,349 - aiogram.event - INFO - Update id=417910553 is handled. Duration 387 ms by bot id=7981191240
2025-06-23 20:10:40,669 - aiogram.event - INFO - Update id=417910554 is handled. Duration 637 ms by bot id=7981191240
2025-06-23 20:10:42,191 - aiogram.event - INFO - Update id=417910555 is handled. Duration 624 ms by bot id=7981191240
2025-06-23 20:10:55,029 - aiogram.event - INFO - Update id=417910556 is handled. Duration 351 ms by bot id=7981191240
2025-06-23 20:10:56,336 - aiogram.event - INFO - Update id=417910557 is handled. Duration 319 ms by bot id=7981191240
2025-06-23 20:10:58,568 - aiogram.event - INFO - Update id=417910558 is handled. Duration 407 ms by bot id=7981191240
2025-06-23 20:11:11,464 - aiogram.event - INFO - Update id=417910559 is handled. Duration 647 ms by bot id=7981191240
2025-06-23 20:12:59,168 - aiogram.event - INFO - Update id=417910560 is handled. Duration 414 ms by bot id=7981191240
2025-06-23 20:13:03,025 - aiogram.event - INFO - Update id=417910561 is handled. Duration 474 ms by bot id=7981191240
2025-06-23 20:13:21,459 - aiogram.event - INFO - Update id=417910562 is handled. Duration 372 ms by bot id=7981191240
2025-06-23 20:13:23,018 - aiogram.event - INFO - Update id=417910563 is handled. Duration 515 ms by bot id=7981191240
2025-06-23 20:13:46,799 - aiogram.event - INFO - Update id=417910564 is handled. Duration 539 ms by bot id=7981191240
2025-06-23 20:13:51,413 - aiogram.event - INFO - Update id=417910565 is handled. Duration 417 ms by bot id=7981191240
2025-06-23 20:14:02,218 - aiogram.event - INFO - Update id=417910566 is handled. Duration 373 ms by bot id=7981191240
2025-06-23 20:14:12,301 - aiogram.event - INFO - Update id=417910567 is handled. Duration 593 ms by bot id=7981191240
2025-06-23 20:14:18,782 - aiogram.event - INFO - Update id=417910568 is handled. Duration 702 ms by bot id=7981191240
2025-06-23 20:14:21,157 - aiogram.event - INFO - Update id=417910569 is handled. Duration 489 ms by bot id=7981191240
2025-06-23 20:14:43,355 - aiogram.event - INFO - Update id=417910570 is handled. Duration 658 ms by bot id=7981191240
2025-06-23 20:14:50,854 - aiogram.event - INFO - Update id=417910571 is handled. Duration 375 ms by bot id=7981191240
2025-06-23 20:15:01,282 - aiogram.event - INFO - Update id=417910572 is handled. Duration 388 ms by bot id=7981191240
2025-06-23 20:15:05,008 - aiogram.event - INFO - Update id=417910573 is handled. Duration 370 ms by bot id=7981191240
2025-06-23 20:15:07,190 - aiogram.event - INFO - Update id=417910574 is handled. Duration 429 ms by bot id=7981191240
2025-06-23 20:15:10,329 - aiogram.event - INFO - Update id=417910575 is handled. Duration 1112 ms by bot id=7981191240
2025-06-23 20:15:39,919 - aiogram.event - INFO - Update id=417910576 is handled. Duration 600 ms by bot id=7981191240
2025-06-23 20:15:58,567 - aiogram.event - INFO - Update id=417910577 is handled. Duration 1023 ms by bot id=7981191240
2025-06-23 20:16:41,460 - aiogram.event - INFO - Update id=417910578 is handled. Duration 358 ms by bot id=7981191240
2025-06-23 20:16:45,248 - aiogram.event - INFO - Update id=417910579 is handled. Duration 916 ms by bot id=7981191240
2025-06-23 20:17:04,178 - aiogram.event - INFO - Update id=417910580 is handled. Duration 508 ms by bot id=7981191240
2025-06-23 20:17:09,637 - aiogram.event - INFO - Update id=417910581 is handled. Duration 375 ms by bot id=7981191240
2025-06-23 20:17:50,807 - aiogram.event - INFO - Update id=417910582 is handled. Duration 428 ms by bot id=7981191240
2025-06-23 20:17:52,210 - aiogram.event - INFO - Update id=417910583 is handled. Duration 367 ms by bot id=7981191240
2025-06-23 20:17:54,238 - aiogram.event - INFO - Update id=417910584 is handled. Duration 585 ms by bot id=7981191240
2025-06-23 20:17:55,862 - aiogram.event - INFO - Update id=417910585 is handled. Duration 558 ms by bot id=7981191240
2025-06-23 20:18:40,836 - aiogram.event - INFO - Update id=417910586 is handled. Duration 693 ms by bot id=7981191240
2025-06-23 20:18:43,001 - aiogram.event - INFO - Update id=417910587 is handled. Duration 503 ms by bot id=7981191240
2025-06-23 20:21:02,640 - aiogram.event - INFO - Update id=417910588 is handled. Duration 530 ms by bot id=7981191240
2025-06-23 20:21:25,141 - aiogram.event - INFO - Update id=417910589 is handled. Duration 542 ms by bot id=7981191240
2025-06-23 20:21:30,679 - aiogram.event - INFO - Update id=417910590 is handled. Duration 348 ms by bot id=7981191240
2025-06-23 20:21:32,564 - aiogram.event - INFO - Update id=417910591 is handled. Duration 430 ms by bot id=7981191240
2025-06-23 20:21:51,229 - aiogram.event - INFO - Update id=417910592 is handled. Duration 583 ms by bot id=7981191240
2025-06-23 20:22:14,288 - aiogram.event - INFO - Update id=417910593 is handled. Duration 322 ms by bot id=7981191240
2025-06-23 20:23:30,769 - aiogram.event - INFO - Update id=417910594 is handled. Duration 354 ms by bot id=7981191240
2025-06-23 20:25:19,365 - aiogram.event - INFO - Update id=417910595 is handled. Duration 426 ms by bot id=7981191240
2025-06-23 20:25:21,215 - aiogram.event - INFO - Update id=417910596 is handled. Duration 408 ms by bot id=7981191240
2025-06-23 20:26:17,109 - aiogram.event - INFO - Update id=417910597 is handled. Duration 441 ms by bot id=7981191240
2025-06-23 20:26:18,642 - aiogram.event - INFO - Update id=417910598 is handled. Duration 463 ms by bot id=7981191240
2025-06-23 20:26:20,110 - aiogram.event - INFO - Update id=417910599 is handled. Duration 386 ms by bot id=7981191240
2025-06-23 20:26:21,546 - aiogram.event - INFO - Update id=417910600 is handled. Duration 428 ms by bot id=7981191240
2025-06-23 20:27:51,293 - aiogram.event - INFO - Update id=417910601 is handled. Duration 500 ms by bot id=7981191240
2025-06-23 20:27:53,243 - aiogram.event - INFO - Update id=417910602 is handled. Duration 438 ms by bot id=7981191240
2025-06-23 20:27:54,424 - aiogram.event - INFO - Update id=417910603 is handled. Duration 593 ms by bot id=7981191240
2025-06-23 20:27:56,978 - aiogram.event - INFO - Update id=417910604 is handled. Duration 806 ms by bot id=7981191240
2025-06-23 20:28:00,681 - aiogram.event - INFO - Update id=417910605 is handled. Duration 377 ms by bot id=7981191240
2025-06-23 20:28:04,757 - aiogram.event - INFO - Update id=417910606 is handled. Duration 398 ms by bot id=7981191240
2025-06-23 20:28:10,192 - aiogram.event - INFO - Update id=417910607 is handled. Duration 322 ms by bot id=7981191240
2025-06-23 20:28:20,396 - aiogram.event - INFO - Update id=417910608 is handled. Duration 522 ms by bot id=7981191240
2025-06-23 20:28:41,652 - aiogram.event - INFO - Update id=417910609 is handled. Duration 472 ms by bot id=7981191240
2025-06-23 20:28:54,940 - aiogram.event - INFO - Update id=417910610 is handled. Duration 591 ms by bot id=7981191240
2025-06-23 20:28:57,847 - aiogram.event - INFO - Update id=417910611 is handled. Duration 433 ms by bot id=7981191240
2025-06-23 20:29:00,217 - aiogram.event - INFO - Update id=417910612 is handled. Duration 693 ms by bot id=7981191240
2025-06-23 20:29:03,520 - aiogram.event - INFO - Update id=417910613 is handled. Duration 574 ms by bot id=7981191240
2025-06-23 20:29:05,997 - aiogram.event - INFO - Update id=417910614 is handled. Duration 761 ms by bot id=7981191240
2025-06-23 20:29:11,073 - aiogram.event - INFO - Update id=417910615 is handled. Duration 343 ms by bot id=7981191240
2025-06-23 20:29:13,823 - aiogram.event - INFO - Update id=417910616 is handled. Duration 401 ms by bot id=7981191240
2025-06-23 20:29:16,536 - aiogram.event - INFO - Update id=417910617 is handled. Duration 346 ms by bot id=7981191240
2025-06-23 20:29:23,309 - aiogram.event - INFO - Update id=417910618 is handled. Duration 400 ms by bot id=7981191240
2025-06-23 20:29:28,372 - aiogram.event - INFO - Update id=417910619 is handled. Duration 362 ms by bot id=7981191240
2025-06-23 20:29:35,122 - aiogram.event - INFO - Update id=417910620 is handled. Duration 471 ms by bot id=7981191240
2025-06-23 20:29:36,812 - aiogram.event - INFO - Update id=417910621 is handled. Duration 833 ms by bot id=7981191240
2025-06-23 20:29:37,877 - aiogram.event - INFO - Update id=417910622 is handled. Duration 338 ms by bot id=7981191240
2025-06-23 20:34:07,840 - aiogram.event - INFO - Update id=417910623 is handled. Duration 849 ms by bot id=7981191240
2025-06-23 20:39:08,839 - aiogram.event - INFO - Update id=417910624 is handled. Duration 536 ms by bot id=7981191240
2025-06-23 20:39:31,514 - aiogram.event - INFO - Update id=417910625 is handled. Duration 910 ms by bot id=7981191240
2025-06-23 20:39:32,694 - aiogram.event - INFO - Update id=417910626 is handled. Duration 353 ms by bot id=7981191240
2025-06-23 20:39:37,949 - aiogram.event - INFO - Update id=417910627 is handled. Duration 435 ms by bot id=7981191240
2025-06-23 20:39:40,090 - aiogram.event - INFO - Update id=417910628 is handled. Duration 678 ms by bot id=7981191240
2025-06-23 20:39:47,911 - aiogram.event - INFO - Update id=417910629 is handled. Duration 408 ms by bot id=7981191240
2025-06-23 20:56:45,168 - aiogram.event - INFO - Update id=417910630 is handled. Duration 774 ms by bot id=7981191240
2025-06-23 20:56:48,171 - aiogram.event - INFO - Update id=417910631 is handled. Duration 555 ms by bot id=7981191240
2025-06-23 20:56:49,189 - aiogram.event - INFO - Update id=417910632 is handled. Duration 345 ms by bot id=7981191240
2025-06-23 20:56:56,427 - aiogram.event - INFO - Update id=417910633 is handled. Duration 632 ms by bot id=7981191240
2025-06-23 20:56:58,495 - aiogram.event - INFO - Update id=417910634 is handled. Duration 429 ms by bot id=7981191240
2025-06-23 20:57:00,940 - aiogram.event - INFO - Update id=417910635 is handled. Duration 441 ms by bot id=7981191240
2025-06-23 20:57:02,857 - aiogram.event - INFO - Update id=417910636 is handled. Duration 602 ms by bot id=7981191240
2025-06-23 20:57:04,006 - aiogram.event - INFO - Update id=417910637 is handled. Duration 521 ms by bot id=7981191240
2025-06-23 20:57:05,848 - aiogram.event - INFO - Update id=417910638 is handled. Duration 721 ms by bot id=7981191240
2025-06-23 20:58:33,317 - aiogram.event - INFO - Update id=417910639 is handled. Duration 537 ms by bot id=7981191240
2025-06-23 20:58:35,767 - aiogram.event - INFO - Update id=417910640 is handled. Duration 648 ms by bot id=7981191240
2025-06-23 20:58:37,440 - aiogram.event - INFO - Update id=417910641 is handled. Duration 781 ms by bot id=7981191240
2025-06-23 20:58:38,555 - aiogram.event - INFO - Update id=417910642 is handled. Duration 340 ms by bot id=7981191240
2025-06-23 20:59:37,784 - aiogram.event - INFO - Update id=417910643 is handled. Duration 373 ms by bot id=7981191240
2025-06-23 20:59:41,768 - aiogram.event - INFO - Update id=417910644 is handled. Duration 393 ms by bot id=7981191240
2025-06-23 20:59:44,979 - aiogram.event - INFO - Update id=417910645 is handled. Duration 387 ms by bot id=7981191240
2025-06-23 20:59:50,074 - aiogram.event - INFO - Update id=417910646 is handled. Duration 453 ms by bot id=7981191240
2025-06-23 21:00:03,822 - aiogram.event - INFO - Update id=417910647 is handled. Duration 709 ms by bot id=7981191240
2025-06-23 21:00:05,422 - aiogram.event - INFO - Update id=417910648 is handled. Duration 316 ms by bot id=7981191240
2025-06-23 21:01:41,472 - aiogram.event - INFO - Update id=417910649 is handled. Duration 431 ms by bot id=7981191240
2025-06-23 21:01:44,078 - aiogram.event - INFO - Update id=417910650 is handled. Duration 706 ms by bot id=7981191240
2025-06-23 21:02:09,807 - aiogram.event - INFO - Update id=417910651 is handled. Duration 502 ms by bot id=7981191240
2025-06-23 21:02:23,379 - aiogram.event - INFO - Update id=417910652 is handled. Duration 335 ms by bot id=7981191240
2025-06-23 21:03:18,942 - __main__ - INFO - Initializing database...
2025-06-23 21:03:19,020 - database.database - INFO - Database tables created successfully
2025-06-23 21:03:19,021 - __main__ - INFO - Initializing product updater...
2025-06-23 21:03:19,021 - parser.file_parser - INFO - Starting initial data load...
2025-06-23 21:03:19,021 - parser.file_parser - INFO - Updating products from data/new_products.txt (condition: new)
2025-06-23 21:03:19,207 - parser.file_parser - INFO - Successfully updated 59 products from data/new_products.txt
2025-06-23 21:03:19,207 - parser.file_parser - INFO - Updating products from data/used_products.txt (condition: used)
2025-06-23 21:03:19,506 - parser.file_parser - INFO - Successfully updated 112 products from data/used_products.txt
2025-06-23 21:03:19,507 - parser.file_parser - INFO - Initial data load completed
2025-06-23 21:03:19,507 - __main__ - INFO - Starting file watcher...
2025-06-23 21:03:19,508 - parser.file_parser - INFO - File watcher started
2025-06-23 21:03:19,508 - __main__ - INFO - Starting bot...
2025-06-23 21:03:19,508 - aiogram.dispatcher - INFO - Start polling
2025-06-23 21:03:20,137 - aiogram.dispatcher - INFO - Run polling for bot @Ostatki_ap43_bot id=7981191240 - 'Остатки Appleshop'
2025-06-23 21:03:51,274 - bot.handlers.user - INFO - User 5513789802 (gabagoolioner) started the bot
2025-06-23 21:03:52,831 - aiogram.event - INFO - Update id=417910653 is handled. Duration 1558 ms by bot id=7981191240
2025-06-23 21:03:55,253 - aiogram.event - INFO - Update id=417910654 is handled. Duration 497 ms by bot id=7981191240
2025-06-23 21:03:56,478 - aiogram.event - INFO - Update id=417910655 is handled. Duration 392 ms by bot id=7981191240
2025-06-23 21:04:04,796 - aiogram.event - INFO - Update id=417910656 is handled. Duration 449 ms by bot id=7981191240
2025-06-23 21:04:08,481 - aiogram.event - INFO - Update id=417910657 is handled. Duration 571 ms by bot id=7981191240
2025-06-23 21:04:11,919 - aiogram.event - INFO - Update id=417910658 is handled. Duration 551 ms by bot id=7981191240
2025-06-23 21:04:13,388 - aiogram.event - INFO - Update id=417910659 is handled. Duration 339 ms by bot id=7981191240
2025-06-23 21:04:18,520 - aiogram.event - INFO - Update id=417910660 is handled. Duration 420 ms by bot id=7981191240
2025-06-23 21:04:23,593 - aiogram.event - INFO - Update id=417910661 is handled. Duration 568 ms by bot id=7981191240
2025-06-23 21:04:35,895 - aiogram.event - INFO - Update id=417910662 is handled. Duration 392 ms by bot id=7981191240
2025-06-23 21:04:38,452 - aiogram.event - INFO - Update id=417910663 is handled. Duration 715 ms by bot id=7981191240
2025-06-23 21:04:40,343 - aiogram.event - INFO - Update id=417910664 is handled. Duration 349 ms by bot id=7981191240
2025-06-23 21:04:45,845 - aiogram.event - INFO - Update id=417910665 is handled. Duration 396 ms by bot id=7981191240
2025-06-23 21:04:47,449 - aiogram.event - INFO - Update id=417910666 is handled. Duration 380 ms by bot id=7981191240
2025-06-23 21:04:48,958 - aiogram.event - INFO - Update id=417910667 is handled. Duration 350 ms by bot id=7981191240
2025-06-23 21:04:50,145 - aiogram.event - INFO - Update id=417910668 is handled. Duration 521 ms by bot id=7981191240
2025-06-23 21:04:52,326 - aiogram.event - INFO - Update id=417910669 is handled. Duration 671 ms by bot id=7981191240
2025-06-23 21:04:57,447 - aiogram.event - INFO - Update id=417910670 is handled. Duration 442 ms by bot id=7981191240
2025-06-23 21:05:03,026 - aiogram.event - INFO - Update id=417910671 is handled. Duration 508 ms by bot id=7981191240
2025-06-23 21:05:05,476 - aiogram.event - INFO - Update id=417910672 is handled. Duration 486 ms by bot id=7981191240
2025-06-23 21:05:10,982 - aiogram.event - INFO - Update id=417910673 is not handled. Duration 20 ms by bot id=7981191240
2025-06-23 21:05:10,983 - aiogram.event - ERROR - Cause exception while process update id=417910673 by bot id=7981191240
TypeError: can only concatenate str (not "tuple") to str
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 332, in sort_products
    text += ProductSorter.format_product_list(products)
TypeError: can only concatenate str (not "tuple") to str
2025-06-23 21:05:19,059 - aiogram.event - INFO - Update id=417910674 is handled. Duration 577 ms by bot id=7981191240
2025-06-23 21:05:32,579 - aiogram.event - INFO - Update id=417910675 is handled. Duration 370 ms by bot id=7981191240
2025-06-23 21:05:35,226 - aiogram.event - INFO - Update id=417910676 is handled. Duration 398 ms by bot id=7981191240
2025-06-23 21:05:56,862 - aiogram.event - INFO - Update id=417910677 is handled. Duration 347 ms by bot id=7981191240
2025-06-23 21:05:59,135 - aiogram.event - INFO - Update id=417910678 is handled. Duration 342 ms by bot id=7981191240
2025-06-23 23:31:05,411 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-06-23 23:31:05,414 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-23 23:31:16,858 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)
2025-06-23 23:32:47,383 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-06-23 23:32:47,387 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-23 23:32:59,266 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)
2025-06-24 00:08:07,651 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - ClientOSError: [Errno 54] Connection reset by peer
2025-06-24 00:08:07,654 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-24 00:08:18,875 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)
2025-06-24 00:20:10,546 - aiogram.event - INFO - Update id=417910680 is handled. Duration 441 ms by bot id=7981191240
2025-06-24 00:20:12,232 - aiogram.event - INFO - Update id=417910681 is handled. Duration 240 ms by bot id=7981191240
2025-06-24 00:20:18,561 - aiogram.event - INFO - Update id=417910682 is not handled. Duration 3 ms by bot id=7981191240
2025-06-24 00:20:26,408 - aiogram.event - INFO - Update id=417910683 is handled. Duration 167 ms by bot id=7981191240
2025-06-24 00:20:28,154 - aiogram.event - INFO - Update id=417910684 is handled. Duration 210 ms by bot id=7981191240
2025-06-24 00:20:30,619 - aiogram.event - INFO - Update id=417910685 is handled. Duration 202 ms by bot id=7981191240
2025-06-24 00:20:32,521 - aiogram.event - INFO - Update id=417910686 is handled. Duration 181 ms by bot id=7981191240
2025-06-24 00:20:40,539 - aiogram.event - INFO - Update id=417910687 is handled. Duration 187 ms by bot id=7981191240
2025-06-24 00:20:44,359 - aiogram.event - INFO - Update id=417910688 is handled. Duration 164 ms by bot id=7981191240
2025-06-24 00:20:45,638 - aiogram.event - INFO - Update id=417910689 is handled. Duration 167 ms by bot id=7981191240
2025-06-24 00:20:46,759 - aiogram.event - INFO - Update id=417910690 is not handled. Duration 2 ms by bot id=7981191240
2025-06-24 00:20:48,408 - aiogram.event - INFO - Update id=417910691 is handled. Duration 158 ms by bot id=7981191240
2025-06-24 00:20:52,717 - aiogram.event - INFO - Update id=417910692 is not handled. Duration 12 ms by bot id=7981191240
2025-06-24 00:20:52,717 - aiogram.event - ERROR - Cause exception while process update id=417910692 by bot id=7981191240
TypeError: can only concatenate str (not "tuple") to str
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 332, in sort_products
    text += ProductSorter.format_product_list(products)
TypeError: can only concatenate str (not "tuple") to str
2025-06-24 00:20:56,321 - aiogram.event - INFO - Update id=417910693 is handled. Duration 166 ms by bot id=7981191240
2025-06-24 00:20:57,817 - aiogram.event - INFO - Update id=417910694 is not handled. Duration 9 ms by bot id=7981191240
2025-06-24 00:20:57,817 - aiogram.event - ERROR - Cause exception while process update id=417910694 by bot id=7981191240
TypeError: can only concatenate str (not "tuple") to str
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 309, in _process_update
    response = await self.feed_update(bot, update, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 158, in feed_update
    response = await self.update.wrap_outer_middleware(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/error.py", line 25, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/middlewares/user_context.py", line 56, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/fsm/middleware.py", line 42, in __call__
    return await handler(event, data)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/dispatcher.py", line 276, in _listen_update
    return await self.propagate_event(update_type=update_type, event=event, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 174, in _propagate_event
    response = await router.propagate_event(update_type=update_type, event=event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 146, in propagate_event
    return await observer.wrap_outer_middleware(_wrapped, event=event, data=kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 141, in _wrapped
    return await self._propagate_event(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        observer=observer, update_type=update_type, event=telegram_event, **data
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/router.py", line 166, in _propagate_event
    response = await observer.trigger(event, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/telegram.py", line 121, in trigger
    return await wrapped_inner(event, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/aiogram/dispatcher/event/handler.py", line 43, in call
    return await wrapped()
           ^^^^^^^^^^^^^^^
  File "/Users/<USER>/appleshopstats/stats/bot/handlers/user.py", line 332, in sort_products
    text += ProductSorter.format_product_list(products)
TypeError: can only concatenate str (not "tuple") to str
2025-06-24 00:21:03,542 - aiogram.event - INFO - Update id=417910695 is handled. Duration 386 ms by bot id=7981191240
2025-06-24 00:21:07,824 - aiogram.event - INFO - Update id=417910696 is handled. Duration 233 ms by bot id=7981191240
2025-06-24 00:21:37,308 - aiogram.event - INFO - Update id=417910697 is handled. Duration 186 ms by bot id=7981191240
2025-06-24 00:21:39,630 - aiogram.event - INFO - Update id=417910698 is handled. Duration 215 ms by bot id=7981191240
2025-06-24 00:21:45,992 - aiogram.event - INFO - Update id=417910699 is handled. Duration 320 ms by bot id=7981191240
2025-06-24 00:22:17,101 - aiogram.event - INFO - Update id=417910700 is handled. Duration 197 ms by bot id=7981191240
2025-06-24 00:22:30,657 - aiogram.event - INFO - Update id=417910701 is handled. Duration 172 ms by bot id=7981191240
2025-06-24 00:22:36,477 - aiogram.event - INFO - Update id=417910702 is handled. Duration 188 ms by bot id=7981191240
2025-06-24 00:25:46,804 - aiogram.event - INFO - Update id=417910703 is handled. Duration 246 ms by bot id=7981191240
2025-06-24 00:25:50,852 - aiogram.event - INFO - Update id=417910704 is handled. Duration 188 ms by bot id=7981191240
2025-06-24 00:25:52,976 - aiogram.event - INFO - Update id=417910705 is handled. Duration 154 ms by bot id=7981191240
2025-06-24 00:25:54,339 - aiogram.event - INFO - Update id=417910706 is handled. Duration 200 ms by bot id=7981191240
2025-06-24 00:26:11,945 - aiogram.event - INFO - Update id=417910707 is handled. Duration 175 ms by bot id=7981191240
2025-06-24 00:26:26,587 - aiogram.event - INFO - Update id=417910708 is handled. Duration 173 ms by bot id=7981191240
2025-06-24 00:26:28,009 - aiogram.event - INFO - Update id=417910709 is handled. Duration 171 ms by bot id=7981191240
2025-06-24 00:26:29,676 - aiogram.event - INFO - Update id=417910710 is handled. Duration 188 ms by bot id=7981191240
2025-06-24 00:26:31,118 - aiogram.event - INFO - Update id=417910711 is handled. Duration 157 ms by bot id=7981191240
2025-06-24 00:26:32,283 - aiogram.event - INFO - Update id=417910712 is handled. Duration 144 ms by bot id=7981191240
2025-06-24 00:26:33,421 - aiogram.event - INFO - Update id=417910713 is handled. Duration 157 ms by bot id=7981191240
2025-06-24 00:26:37,756 - aiogram.event - INFO - Update id=417910714 is handled. Duration 189 ms by bot id=7981191240
2025-06-24 00:26:49,846 - aiogram.event - INFO - Update id=417910715 is handled. Duration 195 ms by bot id=7981191240
2025-06-24 00:26:55,418 - aiogram.event - INFO - Update id=417910716 is handled. Duration 176 ms by bot id=7981191240
2025-06-24 00:27:14,535 - aiogram.event - INFO - Update id=417910717 is handled. Duration 183 ms by bot id=7981191240
2025-06-24 00:27:17,584 - aiogram.event - INFO - Update id=417910718 is handled. Duration 183 ms by bot id=7981191240
2025-06-24 00:27:26,818 - aiogram.event - INFO - Update id=417910719 is handled. Duration 187 ms by bot id=7981191240
2025-06-24 00:27:30,003 - aiogram.event - INFO - Update id=417910720 is handled. Duration 212 ms by bot id=7981191240
2025-06-24 00:27:31,710 - aiogram.event - INFO - Update id=417910721 is handled. Duration 178 ms by bot id=7981191240
2025-06-24 00:27:34,579 - aiogram.event - INFO - Update id=417910722 is handled. Duration 179 ms by bot id=7981191240
2025-06-24 00:31:08,467 - aiogram.event - INFO - Update id=417910723 is handled. Duration 300 ms by bot id=7981191240
2025-06-24 00:34:45,801 - aiogram.event - INFO - Update id=417910724 is handled. Duration 287 ms by bot id=7981191240
2025-06-24 00:34:47,114 - aiogram.event - INFO - Update id=417910725 is handled. Duration 173 ms by bot id=7981191240
2025-06-24 00:34:49,338 - aiogram.event - INFO - Update id=417910726 is handled. Duration 143 ms by bot id=7981191240
2025-06-24 00:34:50,777 - aiogram.event - INFO - Update id=417910727 is handled. Duration 357 ms by bot id=7981191240
2025-06-24 00:34:53,683 - aiogram.event - INFO - Update id=417910728 is handled. Duration 189 ms by bot id=7981191240
2025-06-24 00:35:06,849 - aiogram.event - INFO - Update id=417910729 is handled. Duration 170 ms by bot id=7981191240
2025-06-24 00:35:17,548 - aiogram.event - INFO - Update id=417910730 is handled. Duration 194 ms by bot id=7981191240
2025-06-24 05:28:03,677 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-06-24 05:28:03,679 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-24 05:28:15,011 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)
2025-06-24 08:00:39,365 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-06-24 08:00:39,369 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-24 08:00:50,776 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)
2025-06-24 11:49:28,931 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-06-24 11:49:28,938 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-24 11:49:40,268 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)
2025-06-24 14:14:42,450 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-06-24 14:14:42,455 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-24 14:14:53,772 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)
2025-06-24 16:29:34,808 - aiogram.dispatcher - ERROR - Failed to fetch updates - TelegramNetworkError: HTTP Client says - Request timeout error
2025-06-24 16:29:34,820 - aiogram.dispatcher - WARNING - Sleep for 1.000000 seconds and try again... (tryings = 0, bot id = 7981191240)
2025-06-24 16:29:46,207 - aiogram.dispatcher - INFO - Connection established (tryings = 1, bot id = 7981191240)

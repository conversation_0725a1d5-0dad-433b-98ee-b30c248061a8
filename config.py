import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Telegram Bot
    BOT_TOKEN = os.getenv('BOT_TOKEN')
    
    # Database
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', 5432))
    DB_NAME = os.getenv('DB_NAME', 'appleshop_stats')
    DB_USER = os.getenv('DB_USER', 'appleshop_user')
    DB_PASSWORD = os.getenv('DB_PASSWORD', 'appleshop_password')
    
    @property
    def database_url(self):
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    # Admin
    ADMIN_IDS = [int(id_) for id_ in os.getenv('ADMIN_IDS', '').split(',') if id_.strip()]
    
    # Files
    NEW_PRODUCTS_FILE = os.getenv('NEW_PRODUCTS_FILE', 'data/new_products.txt')
    USED_PRODUCTS_FILE = os.getenv('USED_PRODUCTS_FILE', 'data/used_products.txt')
    
    # Update interval
    FILE_CHECK_INTERVAL = int(os.getenv('FILE_CHECK_INTERVAL', 600))

config = Config()

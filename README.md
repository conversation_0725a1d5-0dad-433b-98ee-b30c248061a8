# Apple Shop Stats Telegram Bot

Telegram-бот для отображения остатков товаров Apple с инлайн-клавиатурой и административными функциями.

## Возможности

### Пользовательские функции
- 📱 Просмотр новых и б/у товаров
- 🔍 Фильтрация по моделям
- 💰 Сортировка по цене (возрастание/убывание)
- 🎨 Цветовая маркировка моделей
- 📊 Отображение диапазонов цен

### Административные функции
- 📊 Статистика товаров
- 🔄 Принудительное обновление данных
- ✏️ Редактирование товаров (в разработке)
- 🔗 Добавление ссылок на посты (в разработке)

## Установка и настройка

### 1. Клонирование и установка зависимостей

```bash
# Установка зависимостей
pip install -r requirements.txt
```

### 2. Настройка базы данных

```bash
# Запуск PostgreSQL через Docker
docker-compose up -d
```

### 3. Настройка переменных окружения

Отредактируйте файл `.env`:

```env
# Telegram Bot Token (получить у @BotFather)
BOT_TOKEN=YOUR_BOT_TOKEN_HERE

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=appleshop_stats
DB_USER=appleshop_user
DB_PASSWORD=appleshop_password

# Admin Configuration (Telegram ID администраторов)
ADMIN_IDS=123456789,987654321

# File Paths
NEW_PRODUCTS_FILE=data/new_products.txt
USED_PRODUCTS_FILE=data/used_products.txt

# Update Interval (seconds)
FILE_CHECK_INTERVAL=600
```

### 4. Формат файлов с товарами

Файлы должны содержать строки в формате:
```
Название товара цена₽
```

Примеры:
```
iPhone 15 Pro Max 256GB Natural Titanium 139990₽
iPhone 14 128GB Midnight 89990₽
iPhone 13 Pro 128GB Graphite (б/у отличное) 79990₽
```

### 5. Запуск бота

```bash
python main.py
```

## Структура проекта

```
stats/
├── requirements.txt          # Зависимости Python
├── .env                     # Переменные окружения
├── config.py               # Конфигурация
├── main.py                 # Точка входа
├── bot/
│   ├── handlers/
│   │   ├── user.py         # Пользовательские хендлеры
│   │   └── admin.py        # Административные хендлеры
│   ├── keyboards/
│   │   └── inline.py       # Инлайн клавиатуры
│   └── utils/
│       ├── colors.py       # Генерация цветов
│       └── sorting.py      # Сортировка товаров
├── database/
│   ├── models.py           # SQLAlchemy модели
│   ├── database.py         # Подключение к БД
│   └── crud.py             # CRUD операции
├── parser/
│   └── file_parser.py      # Парсинг файлов
├── data/
│   ├── new_products.txt    # Новые товары
│   └── used_products.txt   # Б/у товары
└── docker-compose.yml      # PostgreSQL контейнер
```

## Использование

### Команды бота
- `/start` - Запуск бота и главное меню
- `/admin` - Административная панель (только для админов)

### Навигация
1. Выберите категорию: "Новые" или "Б/у"
2. Выберите способ просмотра: "Все товары" или "По моделям"
3. Используйте кнопки сортировки для изменения порядка

### Обновление данных
- Автоматическое: при изменении файлов `data/new_products.txt` и `data/used_products.txt`
- Ручное: через административную панель (`/admin` → "Обновить данные")

## Логирование

Логи сохраняются в файл `bot.log` и выводятся в консоль.

## Разработка

### Добавление новых функций
1. Создайте новые хендлеры в `bot/handlers/`
2. Добавьте новые клавиатуры в `bot/keyboards/`
3. Обновите модели в `database/models.py` при необходимости
4. Зарегистрируйте роутеры в `main.py`

### Тестирование
Рекомендуется создать отдельного тестового бота и использовать отдельную базу данных для разработки.

## Требования
- Python 3.8+
- PostgreSQL 12+
- Docker (для запуска PostgreSQL)

## Лицензия
MIT License

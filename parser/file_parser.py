import os
import re
import asyncio
import logging
from typing import List, Dict, Any
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from database.database import database
from database.crud import ProductCRUD
from config import config

logger = logging.getLogger(__name__)

class ProductFileParser:
    def __init__(self):
        self.price_patterns = [
            r'(\d+(?:\s*\d+)*)\s*₽',  # Цена в рублях
            r'(\d+(?:\s*\d+)*)\s*руб',  # Цена в рублях (альтернативный формат)
            r'(\d+(?:\s*\d+)*)\s*р',   # Цена в рублях (сокращенно)
            r'(\d+(?:\s*\d+)*)\s*$',   # Просто число в конце строки
        ]
    
    def parse_line(self, line: str) -> Dict[str, Any]:
        """Парсинг одной строки файла"""
        line = line.strip()
        if not line:
            return None
        
        # Поиск цены в строке
        price = None
        name = line
        
        for pattern in self.price_patterns:
            match = re.search(pattern, line)
            if match:
                price_str = match.group(1).replace(' ', '')
                try:
                    price = float(price_str)
                    # Удаляем цену из названия
                    name = line[:match.start()].strip()
                    break
                except ValueError:
                    continue
        
        if price is None:
            logger.warning(f"Could not extract price from line: {line}")
            return None
        
        if not name:
            logger.warning(f"Empty product name in line: {line}")
            return None
        
        return {
            'name': name,
            'price': price
        }
    
    async def parse_file(self, file_path: str, condition: str) -> List[Dict[str, Any]]:
        """Парсинг файла с товарами"""
        products = []
        
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            return products
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    try:
                        product_data = self.parse_line(line)
                        if product_data:
                            product_data['condition'] = condition
                            products.append(product_data)
                    except Exception as e:
                        logger.error(f"Error parsing line {line_num} in {file_path}: {e}")
        
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
        
        return products
    
    async def update_products_from_file(self, file_path: str, condition: str):
        """Обновление товаров из файла в базе данных"""
        logger.info(f"Updating products from {file_path} (condition: {condition})")
        
        products = await self.parse_file(file_path, condition)
        
        async for session in database.get_session():
            try:
                # Удаляем все существующие товары данного состояния
                await ProductCRUD.delete_all_products_by_condition(session, condition)
                
                # Добавляем новые товары
                for product_data in products:
                    await ProductCRUD.create_product(session, **product_data)
                
                logger.info(f"Successfully updated {len(products)} products from {file_path}")
                
            except Exception as e:
                logger.error(f"Error updating products from {file_path}: {e}")
                raise

class FileWatcher(FileSystemEventHandler):
    def __init__(self, parser: ProductFileParser):
        self.parser = parser
        self.file_conditions = {
            config.NEW_PRODUCTS_FILE: 'new',
            config.USED_PRODUCTS_FILE: 'used'
        }
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        file_path = event.src_path
        if file_path in self.file_conditions:
            condition = self.file_conditions[file_path]
            logger.info(f"File {file_path} was modified, updating products...")
            
            # Запускаем обновление в новом event loop
            asyncio.create_task(
                self.parser.update_products_from_file(file_path, condition)
            )

class ProductUpdater:
    def __init__(self):
        self.parser = ProductFileParser()
        self.observer = Observer()
        self.watcher = FileWatcher(self.parser)
    
    async def initial_load(self):
        """Первоначальная загрузка данных из файлов"""
        logger.info("Starting initial data load...")
        
        # Загружаем новые товары
        await self.parser.update_products_from_file(
            config.NEW_PRODUCTS_FILE, 'new'
        )
        
        # Загружаем б/у товары
        await self.parser.update_products_from_file(
            config.USED_PRODUCTS_FILE, 'used'
        )
        
        logger.info("Initial data load completed")
    
    def start_watching(self):
        """Запуск мониторинга файлов"""
        # Создаем директории если их нет
        os.makedirs(os.path.dirname(config.NEW_PRODUCTS_FILE), exist_ok=True)
        os.makedirs(os.path.dirname(config.USED_PRODUCTS_FILE), exist_ok=True)
        
        # Настраиваем наблюдение за файлами
        for file_path in [config.NEW_PRODUCTS_FILE, config.USED_PRODUCTS_FILE]:
            directory = os.path.dirname(file_path)
            self.observer.schedule(self.watcher, directory, recursive=False)
        
        self.observer.start()
        logger.info("File watcher started")
    
    def stop_watching(self):
        """Остановка мониторинга файлов"""
        self.observer.stop()
        self.observer.join()
        logger.info("File watcher stopped")

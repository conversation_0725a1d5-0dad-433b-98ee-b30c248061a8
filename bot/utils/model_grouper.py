from typing import List, Dict, Any
import re

class ModelGrouper:
    """Утилита для правильной группировки моделей"""
    
    # Определяем правильный порядок моделей iPhone
    IPHONE_MODELS_ORDER = [
        "8",
        "8 Plus",
        "SE (2020)",
        "SE (2022)",
        "X",
        "XR", 
        "XS",
        "XS Max",
        "11",
        "11 Pro",
        "11 Pro Max",
        "12",
        "12 mini",
        "12 Pro", 
        "12 Pro Max",
        "13",
        "13 mini",
        "13 Pro",
        "13 Pro Max",
        "14",
        "14 Plus",
        "14 Pro",
        "14 Pro Max",
        "15",
        "15 Plus", 
        "15 Pro",
        "15 Pro Max",
        "16",
        "16 Plus",
        "16 Pro",
        "16 Pro Max",
        "16 E",
        "17",
        "17 Slim",
        "17 Pro",
        "17 Pro Max"
    ]
    
    @classmethod
    def normalize_model_name(cls, model: str) -> str:
        """Нормализация названия модели"""
        if not model or model == 'Unknown':
            return None
            
        model = model.strip()
        model_lower = model.lower()
        
        # Проверяем, является ли это AirPods
        if 'airpods' in model_lower or 'air pods' in model_lower:
            return "AirPods"
            
        # Проверяем, является ли это Apple Watch
        if 'watch' in model_lower or 'apple watch' in model_lower:
            return "Apple Watch"
            
        # Проверяем, является ли это iPad
        if 'ipad' in model_lower:
            return "iPad"
            
        # Для iPhone моделей - нормализуем название
        # Убираем "iPhone" из начала, если есть
        if model_lower.startswith('iphone'):
            model = re.sub(r'^iphone\s*', '', model, flags=re.IGNORECASE).strip()
        
        # Нормализуем различные варианты написания
        model = re.sub(r'\s+', ' ', model)  # Убираем лишние пробелы
        
        # Исправляем распространенные ошибки в названиях
        model = re.sub(r'pro\s*max', 'Pro Max', model, flags=re.IGNORECASE)
        model = re.sub(r'pro(?!\s*max)', 'Pro', model, flags=re.IGNORECASE)
        model = re.sub(r'plus', 'Plus', model, flags=re.IGNORECASE)
        model = re.sub(r'mini', 'mini', model, flags=re.IGNORECASE)
        
        # Специальные случаи
        if model.upper() in ['XR', 'XS', 'X', 'SE']:
            return model.upper()
            
        # Проверяем SE модели
        if 'se' in model_lower:
            if '2020' in model:
                return "SE (2020)"
            elif '2022' in model:
                return "SE (2022)"
            else:
                return "SE"
                
        return model
    
    @classmethod
    def group_models_by_category(cls, models_with_prices: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Группировка моделей по категориям с правильным порядком"""
        grouped = {}
        
        for model_data in models_with_prices:
            original_model = model_data['model']
            normalized = cls.normalize_model_name(original_model)
            
            if not normalized:
                continue
                
            if normalized not in grouped:
                grouped[normalized] = {
                    'model': normalized,
                    'min_price': model_data['min_price'],
                    'max_price': model_data['max_price'],
                    'count': 1
                }
            else:
                # Объединяем диапазоны цен
                grouped[normalized]['min_price'] = min(
                    grouped[normalized]['min_price'], 
                    model_data['min_price']
                )
                grouped[normalized]['max_price'] = max(
                    grouped[normalized]['max_price'], 
                    model_data['max_price']
                )
                grouped[normalized]['count'] += 1
        
        # Преобразуем в список и сортируем
        result = list(grouped.values())
        return cls.sort_models_by_priority(result)
    
    @classmethod
    def sort_models_by_priority(cls, models: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Сортировка моделей по приоритету"""
        def get_sort_key(model_data):
            model = model_data['model']
            
            # Специальные категории идут первыми
            if model == "AirPods":
                return (0, 0)
            elif model == "Apple Watch":
                return (0, 1)
            elif model == "iPad":
                return (0, 2)
            elif model in cls.IPHONE_MODELS_ORDER:
                # iPhone модели сортируем по заданному порядку
                return (1, cls.IPHONE_MODELS_ORDER.index(model))
            else:
                # Остальные модели в конец, сортируем по алфавиту
                return (2, model)
        
        return sorted(models, key=get_sort_key)
    
    @classmethod
    def format_model_button_text(cls, model: str, min_price: float, max_price: float) -> str:
        """Форматирование текста кнопки модели БЕЗ цветных эмодзи"""
        if min_price == max_price:
            price_text = f"{int(min_price):,}₽".replace(',', ' ')
        else:
            price_text = f"{int(min_price):,}-{int(max_price):,}₽".replace(',', ' ')
        
        return f"{model} ({price_text})"

import hashlib
from typing import List

class ColorGenerator:
    """Генератор цветов для кнопок на основе хеша названия модели"""
    
    # Набор цветных эмодзи для кнопок
    COLOR_EMOJIS = [
        "🔴", "🟠", "🟡", "🟢", "🔵", "🟣", "🟤", "⚫", "⚪",
        "🔺", "🔻", "🔸", "🔹", "🔶", "🔷", "💎", "💠", "🌟",
        "⭐", "✨", "💫", "🌈", "🎯", "🎪", "🎨", "🎭", "🎪"
    ]
    
    @classmethod
    def get_color_for_model(cls, model: str) -> str:
        """Получение цветного эмодзи для модели на основе хеша"""
        if not model:
            return "📱"
        
        # Создаем хеш от названия модели
        hash_object = hashlib.md5(model.encode())
        hash_hex = hash_object.hexdigest()
        
        # Преобразуем первые 8 символов хеша в число
        hash_int = int(hash_hex[:8], 16)
        
        # Выбираем эмодзи на основе хеша
        emoji_index = hash_int % len(cls.COLOR_EMOJIS)
        return cls.COLOR_EMOJIS[emoji_index]
    
    @classmethod
    def format_model_button_text(cls, model: str, min_price: float, max_price: float) -> str:
        """Форматирование текста кнопки с моделью"""
        emoji = cls.get_color_for_model(model)
        
        if min_price == max_price:
            price_text = f"{int(min_price):,}₽".replace(',', ' ')
        else:
            price_text = f"{int(min_price):,}-{int(max_price):,}₽".replace(',', ' ')
        
        return f"{emoji} {model} ({price_text})"
    
    @classmethod
    def format_condition_button_text(cls, condition: str, min_price: float, max_price: float) -> str:
        """Форматирование текста кнопки с состоянием товара"""
        condition_emoji = "🆕" if condition == "new" else "🔄"
        condition_text = "Новые" if condition == "new" else "Б/у"
        
        if min_price == max_price:
            price_text = f"{int(min_price):,}₽".replace(',', ' ')
        else:
            price_text = f"{int(min_price):,}-{int(max_price):,}₽".replace(',', ' ')
        
        return f"{condition_emoji} {condition_text} ({price_text})"

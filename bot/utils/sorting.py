from typing import List, Dict, Any
from database.models import Product

class ProductSorter:
    """Утилиты для сортировки товаров"""

    @staticmethod
    def sort_products_by_price(products: List[Product], ascending: bool = False) -> List[Product]:
        """Сортировка товаров по цене"""
        return sorted(products, key=lambda p: p.price, reverse=not ascending)

    @staticmethod
    def sort_models_by_price(models_with_prices: List[Dict[str, Any]], ascending: bool = False) -> List[Dict[str, Any]]:
        """Сортировка моделей по минимальной цене"""
        return sorted(
            models_with_prices,
            key=lambda m: m['min_price'],
            reverse=not ascending
        )

    @staticmethod
    def group_products_by_model(products: List[Product]) -> Dict[str, List[Product]]:
        """Группировка товаров по моделям"""
        grouped = {}
        for product in products:
            model = product.model or 'Unknown'
            if model not in grouped:
                grouped[model] = []
            grouped[model].append(product)
        return grouped

    @staticmethod
    def format_product_list(products: List[Product], page: int = 1, items_per_page: int = 20) -> tuple[str, int]:
        """Форматирование списка товаров для отображения с пагинацией"""
        if not products:
            return "Товары не найдены", 1

        total_pages = (len(products) + items_per_page - 1) // items_per_page
        start_idx = (page - 1) * items_per_page
        end_idx = start_idx + items_per_page
        page_products = products[start_idx:end_idx]

        lines = []
        for product in page_products:
            price_formatted = f"{int(product.price):,}₽".replace(',', ' ')
            # Убираем нумерацию, оставляем только название и цену
            lines.append(f"{product.name} - {price_formatted}")

        return "\n".join(lines), total_pages

    @staticmethod
    def format_product_detail(product: Product) -> str:
        """Детальное форматирование информации о товаре"""
        price_formatted = f"{int(product.price):,}₽".replace(',', ' ')
        condition_text = "Новый" if product.condition == "new" else "Б/у"

        text = f"📱 {product.name}\n"
        text += f"💰 Цена: {price_formatted}\n"
        text += f"📊 Состояние: {condition_text}\n"
        text += f"🏷️ Модель: {product.model or 'Не определена'}\n"

        if product.post_link:
            text += f"🔗 Ссылка на пост: {product.post_link}\n"

        return text

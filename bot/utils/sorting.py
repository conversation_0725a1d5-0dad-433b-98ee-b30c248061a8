from typing import List, Dict, Any
from database.models import Product

class ProductSorter:
    """Утилиты для сортировки товаров"""
    
    @staticmethod
    def sort_products_by_price(products: List[Product], ascending: bool = False) -> List[Product]:
        """Сортировка товаров по цене"""
        return sorted(products, key=lambda p: p.price, reverse=not ascending)
    
    @staticmethod
    def sort_models_by_price(models_with_prices: List[Dict[str, Any]], ascending: bool = False) -> List[Dict[str, Any]]:
        """Сортировка моделей по минимальной цене"""
        return sorted(
            models_with_prices, 
            key=lambda m: m['min_price'], 
            reverse=not ascending
        )
    
    @staticmethod
    def group_products_by_model(products: List[Product]) -> Dict[str, List[Product]]:
        """Группировка товаров по моделям"""
        grouped = {}
        for product in products:
            model = product.model or 'Unknown'
            if model not in grouped:
                grouped[model] = []
            grouped[model].append(product)
        return grouped
    
    @staticmethod
    def format_product_list(products: List[Product], max_items: int = 50) -> str:
        """Форматирование списка товаров для отображения"""
        if not products:
            return "Товары не найдены"
        
        lines = []
        for i, product in enumerate(products[:max_items], 1):
            price_formatted = f"{int(product.price):,}₽".replace(',', ' ')
            lines.append(f"{i}. {product.name} - {price_formatted}")
        
        if len(products) > max_items:
            lines.append(f"\n... и еще {len(products) - max_items} товаров")
        
        return "\n".join(lines)
    
    @staticmethod
    def format_product_detail(product: Product) -> str:
        """Детальное форматирование информации о товаре"""
        price_formatted = f"{int(product.price):,}₽".replace(',', ' ')
        condition_text = "Новый" if product.condition == "new" else "Б/у"
        
        text = f"📱 {product.name}\n"
        text += f"💰 Цена: {price_formatted}\n"
        text += f"📊 Состояние: {condition_text}\n"
        text += f"🏷️ Модель: {product.model or 'Не определена'}\n"
        
        if product.post_link:
            text += f"🔗 Ссылка на пост: {product.post_link}\n"
        
        return text

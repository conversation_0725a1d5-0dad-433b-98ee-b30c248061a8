from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.filters import Command
from database.database import database
from database.crud import ProductCRUD, UserCRUD
from bot.keyboards.inline import InlineKeyboards
from parser.file_parser import ProductUpdater
from config import config
import logging

logger = logging.getLogger(__name__)
router = Router()

async def is_admin(user_id: int) -> bool:
    """Проверка, является ли пользователь администратором"""
    if user_id in config.ADMIN_IDS:
        return True

    async with database.get_session() as session:
        return await UserCRUD.is_admin(session, user_id)

@router.message(Command("admin"))
async def admin_command(message: Message):
    """Административная панель"""
    if not await is_admin(message.from_user.id):
        await message.answer("❌ У вас нет прав администратора")
        return

    text = (
        "🔧 Административная панель\n\n"
        "Доступные функции:"
    )

    await message.answer(
        text,
        reply_markup=InlineKeyboards.admin_menu()
    )

@router.callback_query(F.data == "admin_stats")
async def admin_stats(callback: CallbackQuery):
    """Статистика товаров"""
    if not await is_admin(callback.from_user.id):
        await callback.answer("❌ У вас нет прав администратора", show_alert=True)
        return

    async with database.get_session() as session:
        # Получаем статистику по новым товарам
        new_products = await ProductCRUD.get_products_by_condition(session, 'new')
        new_models = await ProductCRUD.get_unique_models_by_condition(session, 'new')
        new_price_range = await ProductCRUD.get_price_range_by_condition(session, 'new')

        # Получаем статистику по б/у товарам
        used_products = await ProductCRUD.get_products_by_condition(session, 'used')
        used_models = await ProductCRUD.get_unique_models_by_condition(session, 'used')
        used_price_range = await ProductCRUD.get_price_range_by_condition(session, 'used')

    text = "📊 Статистика товаров:\n\n"

    # Новые товары
    text += "🆕 Новые товары:\n"
    text += f"   • Количество: {len(new_products)}\n"
    text += f"   • Моделей: {len(new_models)}\n"
    if new_price_range['min_price'] > 0:
        text += f"   • Цены: {int(new_price_range['min_price']):,} - {int(new_price_range['max_price']):,}₽\n"
    else:
        text += "   • Товары отсутствуют\n"

    text += "\n"

    # Б/у товары
    text += "🔄 Б/у товары:\n"
    text += f"   • Количество: {len(used_products)}\n"
    text += f"   • Моделей: {len(used_models)}\n"
    if used_price_range['min_price'] > 0:
        text += f"   • Цены: {int(used_price_range['min_price']):,} - {int(used_price_range['max_price']):,}₽\n"
    else:
        text += "   • Товары отсутствуют\n"

    text = text.replace(',', ' ')

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboards.admin_menu()
    )
    await callback.answer()

@router.callback_query(F.data == "admin_refresh")
async def admin_refresh(callback: CallbackQuery):
    """Принудительное обновление данных"""
    if not await is_admin(callback.from_user.id):
        await callback.answer("❌ У вас нет прав администратора", show_alert=True)
        return

    await callback.answer("🔄 Обновление данных...")

    try:
        updater = ProductUpdater()
        await updater.initial_load()

        await callback.message.edit_text(
            "✅ Данные успешно обновлены!",
            reply_markup=InlineKeyboards.admin_menu()
        )
    except Exception as e:
        logger.error(f"Error refreshing data: {e}")
        await callback.message.edit_text(
            f"❌ Ошибка при обновлении данных:\n{str(e)}",
            reply_markup=InlineKeyboards.admin_menu()
        )

@router.callback_query(F.data == "admin_users")
async def admin_users(callback: CallbackQuery):
    """Информация о пользователях"""
    if not await is_admin(callback.from_user.id):
        await callback.answer("❌ У вас нет прав администратора", show_alert=True)
        return

    # Здесь можно добавить функционал для управления пользователями
    text = (
        "👥 Управление пользователями\n\n"
        "Функционал в разработке..."
    )

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboards.admin_menu()
    )
    await callback.answer()

@router.callback_query(F.data == "admin_logs")
async def admin_logs(callback: CallbackQuery):
    """Просмотр логов"""
    if not await is_admin(callback.from_user.id):
        await callback.answer("❌ У вас нет прав администратора", show_alert=True)
        return

    # Здесь можно добавить функционал для просмотра логов
    text = (
        "📝 Логи системы\n\n"
        "Функционал в разработке..."
    )

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboards.admin_menu()
    )
    await callback.answer()

@router.callback_query(F.data.startswith("edit_product_"))
async def edit_product(callback: CallbackQuery):
    """Редактирование товара"""
    if not await is_admin(callback.from_user.id):
        await callback.answer("❌ У вас нет прав администратора", show_alert=True)
        return

    # product_id = int(callback.data.split("_")[2])

    # Здесь можно добавить функционал для редактирования товара
    await callback.answer("Функционал редактирования в разработке", show_alert=True)

@router.callback_query(F.data.startswith("add_link_"))
async def add_link(callback: CallbackQuery):
    """Добавление ссылки на пост"""
    if not await is_admin(callback.from_user.id):
        await callback.answer("❌ У вас нет прав администратора", show_alert=True)
        return

    # product_id = int(callback.data.split("_")[2])

    # Здесь можно добавить функционал для добавления ссылки
    await callback.answer("Функционал добавления ссылок в разработке", show_alert=True)

from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.filters import CommandStart
from aiogram.exceptions import TelegramBadRequest
from database.database import database
from database.crud import ProductCRUD, UserCRUD
from bot.keyboards.inline import InlineKeyboards
from bot.utils.sorting import ProductSorter
from bot.utils.colors import ColorGenerator
import logging

logger = logging.getLogger(__name__)
router = Router()

@router.message(CommandStart())
async def start_command(message: Message):
    """Обработчик команды /start"""
    # Временно отключаем сохранение пользователей
    # async with database.get_session() as session:
    #     # Создаем или обновляем пользователя
    #     await UserCRUD.create_or_update_user(
    #         session,
    #         telegram_id=message.from_user.id,
    #         username=message.from_user.username,
    #         first_name=message.from_user.first_name,
    #         last_name=message.from_user.last_name
    #     )

    welcome_text = (
        "🍎 Добро пожаловать в Apple Shop Stats!\n\n"
        "Здесь вы можете посмотреть актуальные остатки товаров:\n"
        "• 🆕 Новые устройства\n"
        "• 🔄 Б/у устройства\n\n"
        "Выберите категорию для просмотра:"
    )

    await message.answer(
        welcome_text,
        reply_markup=InlineKeyboards.main_menu()
    )

@router.callback_query(F.data == "back_to_main")
async def back_to_main(callback: CallbackQuery):
    """Возврат в главное меню"""
    welcome_text = (
        "🍎 Apple Shop Stats\n\n"
        "Выберите категорию для просмотра:"
    )

    await callback.message.edit_text(
        welcome_text,
        reply_markup=InlineKeyboards.main_menu()
    )
    await callback.answer()

@router.callback_query(F.data.startswith("condition_"))
async def condition_menu(callback: CallbackQuery):
    """Меню выбора типа просмотра для состояния товара"""
    condition = callback.data.split("_")[1]
    condition_text = "новых" if condition == "new" else "б/у"

    async with database.get_session() as session:
        price_range = await ProductCRUD.get_price_range_by_condition(session, condition)

    if price_range['min_price'] == 0 and price_range['max_price'] == 0:
        await callback.message.edit_text(
            f"❌ {condition_text.title()} товары не найдены",
            reply_markup=InlineKeyboards.main_menu()
        )
        await callback.answer()
        return

    text = (
        f"📱 {condition_text.title()} товары\n\n"
        f"💰 Диапазон цен: {int(price_range['min_price']):,} - {int(price_range['max_price']):,}₽\n\n"
        "Выберите способ просмотра:"
    ).replace(',', ' ')

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboards.condition_menu(condition)
    )
    await callback.answer()

@router.callback_query(F.data.startswith("all_products_"))
async def all_products(callback: CallbackQuery):
    """Показать все товары определенного состояния"""
    condition = callback.data.split("_")[2]

    async with database.get_session() as session:
        products = await ProductCRUD.get_products_by_condition(session, condition)

    if not products:
        condition_text = "новых" if condition == "new" else "б/у"
        await callback.message.edit_text(
            f"❌ {condition_text.title()} товары не найдены",
            reply_markup=InlineKeyboards.condition_menu(condition)
        )
        await callback.answer()
        return

    # Сортируем по убыванию цены по умолчанию
    products = ProductSorter.sort_products_by_price(products, ascending=False)

    condition_text = "новых" if condition == "new" else "б/у"
    text = f"📋 Все {condition_text} товары ({len(products)} шт.):\n\n"
    text += ProductSorter.format_product_list(products)

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboards.products_list_menu(condition)
    )
    await callback.answer()

@router.callback_query(F.data.startswith("by_models_"))
async def by_models(callback: CallbackQuery):
    """Показать товары по моделям"""
    condition = callback.data.split("_")[2]

    async with database.get_session() as session:
        models = await ProductCRUD.get_unique_models_by_condition(session, condition)

        models_with_prices = []
        for model in models:
            price_range = await ProductCRUD.get_price_range_by_model_and_condition(
                session, model, condition
            )
            models_with_prices.append({
                'model': model,
                'min_price': price_range['min_price'],
                'max_price': price_range['max_price']
            })

    if not models_with_prices:
        condition_text = "новых" if condition == "new" else "б/у"
        await callback.message.edit_text(
            f"❌ {condition_text.title()} товары не найдены",
            reply_markup=InlineKeyboards.condition_menu(condition)
        )
        await callback.answer()
        return

    # Сортируем по убыванию минимальной цены по умолчанию
    models_with_prices = ProductSorter.sort_models_by_price(models_with_prices, ascending=False)

    condition_text = "новых" if condition == "new" else "б/у"
    text = f"📱 {condition_text.title()} товары по моделям:\n\nВыберите модель:"

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboards.models_menu(models_with_prices, condition)
    )
    await callback.answer()

@router.callback_query(F.data.startswith("model_"))
async def model_products(callback: CallbackQuery):
    """Показать товары определенной модели"""
    parts = callback.data.split("_", 2)
    condition = parts[1]
    model = parts[2]

    async with database.get_session() as session:
        products = await ProductCRUD.get_products_by_model_and_condition(session, model, condition)

    if not products:
        await callback.message.edit_text(
            f"❌ Товары модели {model} не найдены",
            reply_markup=InlineKeyboards.models_menu([], condition)
        )
        await callback.answer()
        return

    # Сортируем по убыванию цены по умолчанию
    products = ProductSorter.sort_products_by_price(products, ascending=False)

    condition_text = "новых" if condition == "new" else "б/у"
    emoji = ColorGenerator.get_color_for_model(model)
    text = f"{emoji} {model} ({condition_text}, {len(products)} шт.):\n\n"
    text += ProductSorter.format_product_list(products)

    await callback.message.edit_text(
        text,
        reply_markup=InlineKeyboards.products_list_menu(condition, model)
    )
    await callback.answer()

@router.callback_query(F.data.startswith("sort_models_"))
async def sort_models(callback: CallbackQuery):
    """Сортировка моделей по цене"""
    parts = callback.data.split("_")
    condition = parts[2]
    sort_order = parts[3]  # asc или desc

    async with database.get_session() as session:
        models = await ProductCRUD.get_unique_models_by_condition(session, condition)

        models_with_prices = []
        for model in models:
            price_range = await ProductCRUD.get_price_range_by_model_and_condition(
                session, model, condition
            )
            models_with_prices.append({
                'model': model,
                'min_price': price_range['min_price'],
                'max_price': price_range['max_price']
            })

    # Сортируем модели
    ascending = sort_order == "asc"
    models_with_prices = ProductSorter.sort_models_by_price(models_with_prices, ascending)

    condition_text = "новых" if condition == "new" else "б/у"
    sort_text = "возрастанию" if ascending else "убыванию"
    text = f"📱 {condition_text.title()} товары по моделям (по {sort_text} цены):\n\nВыберите модель:"

    try:
        await callback.message.edit_reply_markup(
            reply_markup=InlineKeyboards.models_menu(models_with_prices, condition)
        )
    except TelegramBadRequest:
        await callback.message.edit_text(
            text,
            reply_markup=InlineKeyboards.models_menu(models_with_prices, condition)
        )

    await callback.answer(f"Отсортировано по {sort_text} цены")

@router.callback_query(F.data.startswith("sort_products_"))
async def sort_products(callback: CallbackQuery):
    """Сортировка товаров по цене"""
    parts = callback.data.split("_")
    condition = parts[2]
    model_or_all = parts[3]
    sort_order = parts[4]  # asc или desc

    async with database.get_session() as session:
        if model_or_all == "all":
            products = await ProductCRUD.get_products_by_condition(session, condition)
            model = None
        else:
            products = await ProductCRUD.get_products_by_model_and_condition(session, model_or_all, condition)
            model = model_or_all

    # Сортируем товары
    ascending = sort_order == "asc"
    products = ProductSorter.sort_products_by_price(products, ascending)

    # Формируем текст
    if model:
        emoji = ColorGenerator.get_color_for_model(model)
        condition_text = "новых" if condition == "new" else "б/у"
        text = f"{emoji} {model} ({condition_text}, {len(products)} шт.):\n\n"
    else:
        condition_text = "новых" if condition == "new" else "б/у"
        text = f"📋 Все {condition_text} товары ({len(products)} шт.):\n\n"

    text += ProductSorter.format_product_list(products)

    sort_text = "возрастанию" if ascending else "убыванию"

    try:
        await callback.message.edit_text(
            text,
            reply_markup=InlineKeyboards.products_list_menu(condition, model)
        )
    except TelegramBadRequest:
        pass

    await callback.answer(f"Отсортировано по {sort_text} цены")

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from typing import List, Dict, Any
from bot.utils.colors import ColorGenerator
from bot.utils.model_grouper import ModelGrouper

class InlineKeyboards:
    """Класс для создания инлайн клавиатур"""

    @staticmethod
    def main_menu() -> InlineKeyboardMarkup:
        """Главное меню"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🆕 Новые", callback_data="condition_new"),
                InlineKeyboardButton(text="🔄 Б/у", callback_data="condition_used")
            ]
        ])
        return keyboard

    @staticmethod
    def condition_menu(condition: str) -> InlineKeyboardMarkup:
        """Меню выбора типа просмотра для состояния товара"""
        condition_text = "новых" if condition == "new" else "б/у"

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=f"📋 Все {condition_text} товары",
                    callback_data=f"all_products_{condition}"
                )
            ],
            [
                InlineKeyboardButton(
                    text=f"📱 {condition_text.title()} по моделям",
                    callback_data=f"by_models_{condition}"
                )
            ],
            [
                InlineKeyboardButton(text="⬅️ Назад", callback_data="back_to_main")
            ]
        ])
        return keyboard

    @staticmethod
    def models_menu(models_with_prices: List[Dict[str, Any]], condition: str) -> InlineKeyboardMarkup:
        """Меню выбора модели"""
        keyboard_rows = []

        # Группируем и сортируем модели правильно
        grouped_models = ModelGrouper.group_models_by_category(models_with_prices)

        # Добавляем кнопки моделей (по одной в ряд для лучшей читаемости)
        for model_data in grouped_models:
            button_text = ModelGrouper.format_model_button_text(
                model_data['model'],
                model_data['min_price'],
                model_data['max_price']
            )
            keyboard_rows.append([InlineKeyboardButton(
                text=button_text,
                callback_data=f"model_{condition}_{model_data['model']}"
            )])

        # Кнопки сортировки
        keyboard_rows.append([
            InlineKeyboardButton(text="📈 По возрастанию цены", callback_data=f"sort_models_{condition}_asc"),
            InlineKeyboardButton(text="📉 По убыванию цены", callback_data=f"sort_models_{condition}_desc")
        ])

        # Кнопка назад
        keyboard_rows.append([
            InlineKeyboardButton(text="⬅️ Назад", callback_data=f"condition_{condition}")
        ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    @staticmethod
    def products_list_menu(condition: str, model: str = None) -> InlineKeyboardMarkup:
        """Меню для списка товаров"""
        keyboard_rows = []

        # Кнопки сортировки
        keyboard_rows.append([
            InlineKeyboardButton(text="📈 По возрастанию цены", callback_data=f"sort_products_{condition}_{model or 'all'}_asc"),
            InlineKeyboardButton(text="📉 По убыванию цены", callback_data=f"sort_products_{condition}_{model or 'all'}_desc")
        ])

        # Кнопка назад
        if model:
            keyboard_rows.append([
                InlineKeyboardButton(text="⬅️ К моделям", callback_data=f"by_models_{condition}")
            ])
        else:
            keyboard_rows.append([
                InlineKeyboardButton(text="⬅️ Назад", callback_data=f"condition_{condition}")
            ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    @staticmethod
    def product_detail_menu(product_id: int, condition: str, model: str = None) -> InlineKeyboardMarkup:
        """Меню для детального просмотра товара"""
        keyboard_rows = []

        # Кнопки редактирования (для админов)
        keyboard_rows.append([
            InlineKeyboardButton(text="✏️ Редактировать", callback_data=f"edit_product_{product_id}"),
            InlineKeyboardButton(text="🔗 Добавить ссылку", callback_data=f"add_link_{product_id}")
        ])

        # Кнопка назад
        if model:
            keyboard_rows.append([
                InlineKeyboardButton(text="⬅️ К товарам модели", callback_data=f"model_{condition}_{model}")
            ])
        else:
            keyboard_rows.append([
                InlineKeyboardButton(text="⬅️ К списку товаров", callback_data=f"all_products_{condition}")
            ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    @staticmethod
    def admin_menu() -> InlineKeyboardMarkup:
        """Административное меню"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="📊 Статистика", callback_data="admin_stats"),
                InlineKeyboardButton(text="🔄 Обновить данные", callback_data="admin_refresh")
            ],
            [
                InlineKeyboardButton(text="👥 Пользователи", callback_data="admin_users"),
                InlineKeyboardButton(text="📝 Логи", callback_data="admin_logs")
            ],
            [
                InlineKeyboardButton(text="⬅️ В главное меню", callback_data="back_to_main")
            ]
        ])
        return keyboard

    @staticmethod
    def pagination_menu(current_page: int, total_pages: int, callback_prefix: str) -> InlineKeyboardMarkup:
        """Меню пагинации"""
        keyboard_rows = []

        if total_pages > 1:
            row = []

            # Кнопка "Предыдущая"
            if current_page > 1:
                row.append(InlineKeyboardButton(
                    text="⬅️ Пред.",
                    callback_data=f"{callback_prefix}_page_{current_page - 1}"
                ))

            # Информация о странице
            row.append(InlineKeyboardButton(
                text=f"{current_page}/{total_pages}",
                callback_data="page_info"
            ))

            # Кнопка "Следующая"
            if current_page < total_pages:
                row.append(InlineKeyboardButton(
                    text="След. ➡️",
                    callback_data=f"{callback_prefix}_page_{current_page + 1}"
                ))

            keyboard_rows.append(row)

        return InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

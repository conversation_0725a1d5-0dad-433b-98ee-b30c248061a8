import asyncio
import logging
import sys
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode
from database.database import database
from parser.file_parser import ProductUpdater
from bot.handlers import user, admin
from config import config

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """Главная функция запуска бота"""
    
    # Проверяем наличие токена
    if not config.BOT_TOKEN:
        logger.error("BOT_TOKEN not found in environment variables")
        return
    
    # Создаем бота и диспетчер
    bot = <PERSON><PERSON>(
        token=config.BOT_TOKEN,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML)
    )
    
    dp = Dispatcher()
    
    # Регистрируем роутеры
    dp.include_router(user.router)
    dp.include_router(admin.router)
    
    try:
        # Инициализируем базу данных
        logger.info("Initializing database...")
        await database.create_tables()
        
        # Инициализируем парсер и загружаем данные
        logger.info("Initializing product updater...")
        updater = ProductUpdater()
        await updater.initial_load()
        
        # Запускаем мониторинг файлов
        logger.info("Starting file watcher...")
        updater.start_watching()
        
        # Запускаем бота
        logger.info("Starting bot...")
        await dp.start_polling(bot)
        
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        raise
    finally:
        # Закрываем соединения
        await database.close()
        await bot.session.close()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
